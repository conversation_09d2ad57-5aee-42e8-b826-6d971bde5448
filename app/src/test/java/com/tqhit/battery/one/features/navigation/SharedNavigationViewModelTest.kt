package com.tqhit.battery.one.features.navigation

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.tqhit.battery.one.R
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Rule
import org.junit.Test

/**
 * Unit tests for SharedNavigationViewModel.
 * Tests fragment state management, transition events, and performance tracking.
 */
@ExperimentalCoroutinesApi
class SharedNavigationViewModelTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private lateinit var viewModel: SharedNavigationViewModel

    @Before
    fun setup() {
        viewModel = SharedNavigationViewModel()
    }

    @Test
    fun `initial state should be animation fragment`() = runTest {
        // Given - fresh ViewModel instance
        
        // When - checking initial state
        val initialFragmentId = viewModel.activeFragmentId.first()
        val currentActiveId = viewModel.getCurrentActiveFragmentId()
        
        // Then - should default to animation fragment
        assertEquals(R.id.animationGridFragment, initialFragmentId)
        assertEquals(R.id.animationGridFragment, currentActiveId)
        assertTrue(viewModel.isFragmentActive(R.id.animationGridFragment))
        assertFalse(viewModel.isFragmentActive(R.id.dischargeFragment))
    }

    @Test
    fun `setActiveFragment should update state correctly`() = runTest {
        // Given - initial state
        val initialId = viewModel.activeFragmentId.first()
        assertEquals(R.id.animationGridFragment, initialId)
        
        // When - setting new active fragment
        viewModel.setActiveFragment(R.id.dischargeFragment, "Test transition")
        
        // Then - state should be updated
        val newId = viewModel.activeFragmentId.first()
        assertEquals(R.id.dischargeFragment, newId)
        assertEquals(R.id.dischargeFragment, viewModel.getCurrentActiveFragmentId())
        assertTrue(viewModel.isFragmentActive(R.id.dischargeFragment))
        assertFalse(viewModel.isFragmentActive(R.id.animationGridFragment))
    }

    @Test
    fun `setActiveFragment with same ID should not emit new value`() = runTest {
        // Given - initial state
        val initialId = viewModel.activeFragmentId.first()
        assertEquals(R.id.animationGridFragment, initialId)
        
        // When - setting same fragment ID
        viewModel.setActiveFragment(R.id.animationGridFragment, "Same fragment")
        
        // Then - state should remain unchanged
        val currentId = viewModel.activeFragmentId.first()
        assertEquals(R.id.animationGridFragment, currentId)
        assertTrue(viewModel.isFragmentActive(R.id.animationGridFragment))
    }

    @Test
    fun `fragment transition events should be emitted correctly`() = runTest {
        // Given - initial state
        val initialTransition = viewModel.fragmentTransition.first()
        assertNull(initialTransition)
        
        // When - changing fragment
        viewModel.setActiveFragment(R.id.chargeFragment, "User navigation")
        
        // Then - transition event should be emitted
        val transition = viewModel.fragmentTransition.first()
        assertNotNull(transition)
        assertEquals(R.id.animationGridFragment, transition!!.fromFragmentId)
        assertEquals(R.id.chargeFragment, transition.toFragmentId)
        assertEquals("User navigation", transition.reason)
        assertTrue(transition.timestamp > 0)
    }

    @Test
    fun `multiple fragment transitions should work correctly`() = runTest {
        // Given - initial state
        assertEquals(R.id.animationGridFragment, viewModel.activeFragmentId.first())
        
        // When - performing multiple transitions
        viewModel.setActiveFragment(R.id.dischargeFragment, "Dynamic switch")
        assertEquals(R.id.dischargeFragment, viewModel.activeFragmentId.first())
        
        viewModel.setActiveFragment(R.id.healthFragment, "User navigation")
        assertEquals(R.id.healthFragment, viewModel.activeFragmentId.first())
        
        viewModel.setActiveFragment(R.id.settingsFragment, "User navigation")
        assertEquals(R.id.settingsFragment, viewModel.activeFragmentId.first())
        
        // Then - final state should be correct
        assertTrue(viewModel.isFragmentActive(R.id.settingsFragment))
        assertFalse(viewModel.isFragmentActive(R.id.healthFragment))
        assertFalse(viewModel.isFragmentActive(R.id.dischargeFragment))
        assertFalse(viewModel.isFragmentActive(R.id.animationGridFragment))
    }

    @Test
    fun `performance stats should track transitions correctly`() = runTest {
        // Given - initial state
        val initialStats = viewModel.getPerformanceStats()
        assertTrue(initialStats.contains("Transitions: 0"))
        assertTrue(initialStats.contains("Active: AnimationGridFragment"))
        
        // When - performing transitions
        viewModel.setActiveFragment(R.id.dischargeFragment, "First transition")
        viewModel.setActiveFragment(R.id.chargeFragment, "Second transition")
        
        // Then - stats should reflect transitions
        val finalStats = viewModel.getPerformanceStats()
        assertTrue(finalStats.contains("Transitions: 2"))
        assertTrue(finalStats.contains("Active: ChargeFragment"))
        assertTrue(finalStats.contains("Last transition:"))
    }

    @Test
    fun `isFragmentActive should work for all fragment types`() = runTest {
        // Test all fragment IDs
        val fragmentIds = listOf(
            R.id.animationGridFragment,
            R.id.chargeFragment,
            R.id.dischargeFragment,
            R.id.healthFragment,
            R.id.settingsFragment,
            R.id.othersFragment
        )
        
        for (fragmentId in fragmentIds) {
            // When - setting each fragment as active
            viewModel.setActiveFragment(fragmentId, "Test fragment $fragmentId")
            
            // Then - only that fragment should be active
            assertTrue("Fragment $fragmentId should be active", viewModel.isFragmentActive(fragmentId))
            
            // And all others should be inactive
            for (otherId in fragmentIds) {
                if (otherId != fragmentId) {
                    assertFalse("Fragment $otherId should be inactive when $fragmentId is active", 
                        viewModel.isFragmentActive(otherId))
                }
            }
        }
    }

    @Test
    fun `transition timing should be tracked correctly`() = runTest {
        // Given - initial state
        val startTime = System.currentTimeMillis()
        
        // When - performing transition
        viewModel.setActiveFragment(R.id.dischargeFragment, "Timing test")
        
        // Then - transition timestamp should be reasonable
        val transition = viewModel.fragmentTransition.first()
        assertNotNull(transition)
        assertTrue("Transition timestamp should be after start time", 
            transition!!.timestamp >= startTime)
        assertTrue("Transition timestamp should be recent", 
            transition.timestamp <= System.currentTimeMillis())
    }

    @Test
    fun `performance stats should include timing information`() = runTest {
        // Given - initial state
        Thread.sleep(10) // Small delay to ensure timing difference
        
        // When - performing transition
        viewModel.setActiveFragment(R.id.healthFragment, "Performance test")
        
        // Then - stats should include timing
        val stats = viewModel.getPerformanceStats()
        assertTrue("Stats should contain transition count", stats.contains("Transitions: 1"))
        assertTrue("Stats should contain active fragment", stats.contains("Active: HealthFragment"))
        assertTrue("Stats should contain timing info", stats.contains("Last transition:"))
        assertTrue("Stats should contain time unit", stats.contains("ms ago"))
    }
}
