package com.tqhit.battery.one.features.navigation

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.tqhit.battery.one.R
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Rule
import org.junit.Test

/**
 * Unit tests for fragment observation patterns with SharedNavigationViewModel.
 * Tests how fragments should observe and react to navigation state changes.
 */
@ExperimentalCoroutinesApi
class FragmentNavigationObservationTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private lateinit var viewModel: SharedNavigationViewModel

    @Before
    fun setup() {
        viewModel = SharedNavigationViewModel()
    }

    @Test
    fun `fragment should receive initial state on observation`() = runTest {
        // Given - fresh ViewModel
        
        // When - fragment starts observing
        val initialState = viewModel.activeFragmentId.first()
        
        // Then - should receive default state
        assertEquals(R.id.animationGridFragment, initialState)
    }

    @Test
    fun `fragment should receive state changes when observed`() = runTest {
        // Given - fragment observing state
        val stateChanges = mutableListOf<Int>()
        
        // When - collecting state changes
        val job = kotlinx.coroutines.launch {
            viewModel.activeFragmentId.take(3).toList().let { states ->
                stateChanges.addAll(states)
            }
        }
        
        // Trigger state changes
        viewModel.setActiveFragment(R.id.dischargeFragment, "Test 1")
        viewModel.setActiveFragment(R.id.chargeFragment, "Test 2")
        
        job.join()
        
        // Then - should receive all state changes
        assertEquals(3, stateChanges.size)
        assertEquals(R.id.animationGridFragment, stateChanges[0]) // Initial
        assertEquals(R.id.dischargeFragment, stateChanges[1])     // First change
        assertEquals(R.id.chargeFragment, stateChanges[2])        // Second change
    }

    @Test
    fun `multiple fragments should receive same state changes`() = runTest {
        // Given - multiple fragments observing
        val fragment1States = mutableListOf<Int>()
        val fragment2States = mutableListOf<Int>()
        val fragment3States = mutableListOf<Int>()
        
        // When - all fragments observe state
        val job1 = kotlinx.coroutines.launch {
            viewModel.activeFragmentId.take(2).toList().let { states ->
                fragment1States.addAll(states)
            }
        }
        
        val job2 = kotlinx.coroutines.launch {
            viewModel.activeFragmentId.take(2).toList().let { states ->
                fragment2States.addAll(states)
            }
        }
        
        val job3 = kotlinx.coroutines.launch {
            viewModel.activeFragmentId.take(2).toList().let { states ->
                fragment3States.addAll(states)
            }
        }
        
        // Trigger state change
        viewModel.setActiveFragment(R.id.healthFragment, "Multi-fragment test")
        
        job1.join()
        job2.join()
        job3.join()
        
        // Then - all fragments should receive same states
        assertEquals(fragment1States, fragment2States)
        assertEquals(fragment2States, fragment3States)
        assertEquals(2, fragment1States.size)
        assertEquals(R.id.animationGridFragment, fragment1States[0])
        assertEquals(R.id.healthFragment, fragment1States[1])
    }

    @Test
    fun `fragment should be able to determine if it is active`() = runTest {
        // Given - fragment with specific ID
        val dischargeFragmentId = R.id.dischargeFragment
        
        // When - checking if fragment is active initially
        val initiallyActive = viewModel.isFragmentActive(dischargeFragmentId)
        
        // Then - should not be active initially
        assertFalse(initiallyActive)
        
        // When - fragment becomes active
        viewModel.setActiveFragment(dischargeFragmentId, "Fragment activation test")
        val nowActive = viewModel.isFragmentActive(dischargeFragmentId)
        
        // Then - should be active now
        assertTrue(nowActive)
        
        // When - different fragment becomes active
        viewModel.setActiveFragment(R.id.chargeFragment, "Different fragment")
        val stillActive = viewModel.isFragmentActive(dischargeFragmentId)
        
        // Then - should not be active anymore
        assertFalse(stillActive)
    }

    @Test
    fun `fragment observation should handle rapid state changes`() = runTest {
        // Given - fragment observing rapid changes
        val stateChanges = mutableListOf<Int>()
        
        // When - collecting rapid state changes
        val job = kotlinx.coroutines.launch {
            viewModel.activeFragmentId.take(6).toList().let { states ->
                stateChanges.addAll(states)
            }
        }
        
        // Trigger rapid state changes
        viewModel.setActiveFragment(R.id.dischargeFragment, "Rapid 1")
        viewModel.setActiveFragment(R.id.chargeFragment, "Rapid 2")
        viewModel.setActiveFragment(R.id.healthFragment, "Rapid 3")
        viewModel.setActiveFragment(R.id.settingsFragment, "Rapid 4")
        viewModel.setActiveFragment(R.id.animationGridFragment, "Rapid 5")
        
        job.join()
        
        // Then - should receive all changes in order
        assertEquals(6, stateChanges.size)
        assertEquals(R.id.animationGridFragment, stateChanges[0]) // Initial
        assertEquals(R.id.dischargeFragment, stateChanges[1])
        assertEquals(R.id.chargeFragment, stateChanges[2])
        assertEquals(R.id.healthFragment, stateChanges[3])
        assertEquals(R.id.settingsFragment, stateChanges[4])
        assertEquals(R.id.animationGridFragment, stateChanges[5])
    }

    @Test
    fun `fragment should handle transition events correctly`() = runTest {
        // Given - fragment observing transitions
        val transitions = mutableListOf<FragmentTransition?>()
        
        // When - collecting transition events
        val job = kotlinx.coroutines.launch {
            viewModel.fragmentTransition.take(3).toList().let { events ->
                transitions.addAll(events)
            }
        }
        
        // Trigger transitions
        viewModel.setActiveFragment(R.id.dischargeFragment, "First transition")
        viewModel.setActiveFragment(R.id.chargeFragment, "Second transition")
        
        job.join()
        
        // Then - should receive transition events
        assertEquals(3, transitions.size)
        assertNull(transitions[0]) // Initial null state
        
        val firstTransition = transitions[1]
        assertNotNull(firstTransition)
        assertEquals(R.id.animationGridFragment, firstTransition!!.fromFragmentId)
        assertEquals(R.id.dischargeFragment, firstTransition.toFragmentId)
        assertEquals("First transition", firstTransition.reason)
        
        val secondTransition = transitions[2]
        assertNotNull(secondTransition)
        assertEquals(R.id.dischargeFragment, secondTransition!!.fromFragmentId)
        assertEquals(R.id.chargeFragment, secondTransition.toFragmentId)
        assertEquals("Second transition", secondTransition.reason)
    }

    @Test
    fun `fragment lifecycle simulation should work correctly`() = runTest {
        // Simulate fragment lifecycle with SharedNavigationViewModel
        
        // Given - fragment starts observing (onViewCreated)
        var isFragmentActive = false
        var activationCount = 0
        var deactivationCount = 0
        
        val job = kotlinx.coroutines.launch {
            viewModel.activeFragmentId.collect { activeFragmentId ->
                val wasActive = isFragmentActive
                isFragmentActive = activeFragmentId == R.id.dischargeFragment
                
                if (!wasActive && isFragmentActive) {
                    activationCount++
                    // Simulate onFragmentVisible()
                } else if (wasActive && !isFragmentActive) {
                    deactivationCount++
                    // Simulate onFragmentHidden()
                }
            }
        }
        
        // When - simulating navigation events
        assertFalse(isFragmentActive) // Initially inactive
        
        viewModel.setActiveFragment(R.id.dischargeFragment, "Fragment becomes active")
        kotlinx.coroutines.delay(10) // Allow collection
        assertTrue(isFragmentActive)
        assertEquals(1, activationCount)
        assertEquals(0, deactivationCount)
        
        viewModel.setActiveFragment(R.id.chargeFragment, "Fragment becomes inactive")
        kotlinx.coroutines.delay(10) // Allow collection
        assertFalse(isFragmentActive)
        assertEquals(1, activationCount)
        assertEquals(1, deactivationCount)
        
        viewModel.setActiveFragment(R.id.dischargeFragment, "Fragment active again")
        kotlinx.coroutines.delay(10) // Allow collection
        assertTrue(isFragmentActive)
        assertEquals(2, activationCount)
        assertEquals(1, deactivationCount)
        
        job.cancel()
    }
}
