package com.tqhit.battery.one.fragment.main.others

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.fragment.app.testing.FragmentScenario
import androidx.fragment.app.testing.launchFragmentInContainer
import androidx.lifecycle.Lifecycle
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.navigation.DynamicNavigationManager
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.fragment.main.others.data.OthersItemData
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

/**
 * Unit tests for OthersFragment.
 * Tests battery state changes, navigation logic, and UI updates.
 * 
 * Following the established testing patterns with Hilt, MockK, and Robolectric.
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
@Config(sdk = [28])
class OthersFragmentTest {

    @get:Rule
    val hiltRule = HiltAndroidRule(this)

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    @MockK
    private lateinit var mockCoreBatteryStatsProvider: CoreBatteryStatsProvider

    @MockK
    private lateinit var mockDynamicNavigationManager: DynamicNavigationManager

    @MockK
    private lateinit var mockAppViewModel: AppViewModel

    private lateinit var batteryStatusFlow: MutableStateFlow<CoreBatteryStatus?>
    private lateinit var scenario: FragmentScenario<OthersFragment>

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        hiltRule.inject()

        // Setup battery status flow
        batteryStatusFlow = MutableStateFlow(null)
        every { mockCoreBatteryStatsProvider.coreBatteryStatusFlow } returns batteryStatusFlow

        // Setup default mock behaviors
        every { mockDynamicNavigationManager.isInitialized() } returns true
        every { mockDynamicNavigationManager.handleUserNavigation(any()) } returns true
        every { mockAppViewModel.isAntiThiefEnabled() } returns false
        every { mockAppViewModel.isAntiThiefPasswordSet() } returns false
        every { mockAppViewModel.setAntiThiefEnabled(any()) } returns Unit
        every { mockAppViewModel.setAntiThiefPassword(any()) } returns Unit
    }

    @Test
    fun `fragment initializes correctly with default state`() = runTest {
        // Given
        val initialBatteryStatus = createMockBatteryStatus(isCharging = false)
        batteryStatusFlow.value = initialBatteryStatus

        // When
        scenario = launchFragmentInContainer<OthersFragment>()

        // Then
        scenario.moveToState(Lifecycle.State.RESUMED)
        
        // Verify battery status observation is set up
        verify { mockCoreBatteryStatsProvider.coreBatteryStatusFlow }
    }

    @Test
    fun `adapter items update when battery charging state changes`() = runTest {
        // Given
        scenario = launchFragmentInContainer<OthersFragment>()
        scenario.moveToState(Lifecycle.State.RESUMED)

        // When - Device starts charging
        val chargingStatus = createMockBatteryStatus(isCharging = true)
        batteryStatusFlow.value = chargingStatus

        // Then - Should create discharge item when charging
        scenario.onFragment { fragment ->
            // Verify the fragment updates its internal state
            // Note: In a real test, we'd verify the adapter contents
        }

        // When - Device stops charging
        val dischargingStatus = createMockBatteryStatus(isCharging = false)
        batteryStatusFlow.value = dischargingStatus

        // Then - Should create charge item when not charging
        scenario.onFragment { fragment ->
            // Verify the fragment updates its internal state
        }
    }

    @Test
    fun `charge discharge navigation routes correctly based on battery state`() = runTest {
        // Given - Device is charging
        val chargingStatus = createMockBatteryStatus(isCharging = true)
        batteryStatusFlow.value = chargingStatus
        
        scenario = launchFragmentInContainer<OthersFragment>()
        scenario.moveToState(Lifecycle.State.RESUMED)

        // When - User clicks charge/discharge item
        scenario.onFragment { fragment ->
            val chargeDischargeItem = OthersItemData(
                id = OthersItemData.CHARGE_DISCHARGE_ITEM_ID,
                title = "Discharge",
                description = "Test",
                iconResId = R.drawable.ic_discharge_icon
            )
            // Simulate item click - would normally test through UI interaction
        }

        // Then - Should navigate to discharge fragment
        verify { mockDynamicNavigationManager.handleUserNavigation(R.id.dischargeFragment) }
    }

    @Test
    fun `health navigation routes to health fragment`() = runTest {
        // Given
        scenario = launchFragmentInContainer<OthersFragment>()
        scenario.moveToState(Lifecycle.State.RESUMED)

        // When - User clicks health item
        scenario.onFragment { fragment ->
            val healthItem = OthersItemData(
                id = OthersItemData.HEALTH_ITEM_ID,
                title = "Health",
                description = "Test",
                iconResId = R.drawable.ic_health_icon
            )
            // Simulate item click
        }

        // Then - Should navigate to health fragment
        // Note: In real implementation, we'd verify fragment transaction
    }

    @Test
    fun `settings navigation routes to settings fragment`() = runTest {
        // Given
        scenario = launchFragmentInContainer<OthersFragment>()
        scenario.moveToState(Lifecycle.State.RESUMED)

        // When - User clicks settings item
        scenario.onFragment { fragment ->
            val settingsItem = OthersItemData(
                id = OthersItemData.SETTINGS_ITEM_ID,
                title = "Settings",
                description = "Test",
                iconResId = R.drawable.ic_settings_icon
            )
            // Simulate item click
        }

        // Then - Should navigate to settings fragment
        // Note: In real implementation, we'd verify fragment transaction
    }

    @Test
    fun `anti-theft toggle enables correctly when password is set`() = runTest {
        // Given - Password is already set
        every { mockAppViewModel.isAntiThiefPasswordSet() } returns true
        
        scenario = launchFragmentInContainer<OthersFragment>()
        scenario.moveToState(Lifecycle.State.RESUMED)

        // When - User enables anti-theft toggle
        scenario.onFragment { fragment ->
            // Simulate toggle change
        }

        // Then - Should enable anti-theft directly
        verify { mockAppViewModel.setAntiThiefEnabled(true) }
    }

    @Test
    fun `anti-theft toggle shows password dialog when no password set`() = runTest {
        // Given - No password set
        every { mockAppViewModel.isAntiThiefPasswordSet() } returns false
        
        scenario = launchFragmentInContainer<OthersFragment>()
        scenario.moveToState(Lifecycle.State.RESUMED)

        // When - User enables anti-theft toggle
        scenario.onFragment { fragment ->
            // Simulate toggle change
        }

        // Then - Should show password setup dialog
        // Note: In real implementation, we'd verify dialog is shown
    }

    @Test
    fun `fragment handles battery status observation errors gracefully`() = runTest {
        // Given - Battery status flow throws exception
        every { mockCoreBatteryStatsProvider.coreBatteryStatusFlow } throws RuntimeException("Test error")
        
        // When
        scenario = launchFragmentInContainer<OthersFragment>()
        scenario.moveToState(Lifecycle.State.RESUMED)

        // Then - Fragment should handle error and continue functioning
        // Note: In real implementation, we'd verify error handling
    }

    @Test
    fun `dynamic navigation manager fallback works when not initialized`() = runTest {
        // Given - DynamicNavigationManager is not initialized
        every { mockDynamicNavigationManager.isInitialized() } returns false
        
        scenario = launchFragmentInContainer<OthersFragment>()
        scenario.moveToState(Lifecycle.State.RESUMED)

        // When - User tries to navigate
        scenario.onFragment { fragment ->
            // Simulate navigation attempt
        }

        // Then - Should handle gracefully without crashing
        // Note: In real implementation, we'd verify fallback behavior
    }

    private fun createMockBatteryStatus(
        isCharging: Boolean,
        percentage: Int = 50,
        pluggedSource: Int = 0,
        currentMicroAmperes: Long = 0L,
        voltageMillivolts: Int = 4000,
        temperatureCelsius: Float = 25.0f
    ): CoreBatteryStatus {
        return CoreBatteryStatus(
            percentage = percentage,
            isCharging = isCharging,
            pluggedSource = pluggedSource,
            currentMicroAmperes = currentMicroAmperes,
            voltageMillivolts = voltageMillivolts,
            temperatureCelsius = temperatureCelsius,
            timestampEpochMillis = System.currentTimeMillis()
        )
    }
}
