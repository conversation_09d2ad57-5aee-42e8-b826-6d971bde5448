package com.tqhit.battery.one.features.navigation

import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.LifecycleOwner
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import io.mockk.*
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for AppNavigator following the established stats module architecture pattern.
 * 
 * Tests cover:
 * - Initialization validation
 * - All navigation methods with success and failure scenarios
 * - Battery state validation for dynamic navigation
 * - Fallback mechanisms
 * - Error handling and edge cases
 * - Performance tracking
 * 
 * Uses MockK for comprehensive mocking of dependencies.
 */
class AppNavigatorTest {
    
    // Test dependencies
    private lateinit var appNavigator: AppNavigator
    private lateinit var mockDynamicNavigationManager: DynamicNavigationManager
    private lateinit var mockCoreBatteryStatsProvider: CoreBatteryStatsProvider
    private lateinit var mockFragmentManager: FragmentManager
    private lateinit var mockBottomNavigationView: BottomNavigationView
    private lateinit var mockLifecycleOwner: LifecycleOwner
    private lateinit var mockFragmentTransaction: FragmentTransaction
    
    // Test constants
    private val testFragmentContainerId = R.id.nav_host_fragment
    
    @Before
    fun setUp() {
        // Create mocks
        mockDynamicNavigationManager = mockk(relaxed = true)
        mockCoreBatteryStatsProvider = mockk(relaxed = true)
        mockFragmentManager = mockk(relaxed = true)
        mockBottomNavigationView = mockk(relaxed = true)
        mockLifecycleOwner = mockk(relaxed = true)
        mockFragmentTransaction = mockk(relaxed = true)
        
        // Setup FragmentManager mock behavior
        every { mockFragmentManager.beginTransaction() } returns mockFragmentTransaction
        every { mockFragmentTransaction.replace(any(), any<androidx.fragment.app.Fragment>(), any()) } returns mockFragmentTransaction
        every { mockFragmentTransaction.replace(any(), any<androidx.fragment.app.Fragment>()) } returns mockFragmentTransaction
        every { mockFragmentTransaction.addToBackStack(any()) } returns mockFragmentTransaction
        every { mockFragmentTransaction.commit() } returns 1
        every { mockFragmentManager.backStackEntryCount } returns 1
        every { mockFragmentManager.popBackStack() } just Runs
        
        // Create AppNavigator instance
        appNavigator = AppNavigator(mockDynamicNavigationManager, mockCoreBatteryStatsProvider)
    }
    
    @After
    fun tearDown() {
        clearAllMocks()
    }
    
    @Test
    fun `initialization sets up AppNavigator correctly`() {
        // Act
        appNavigator.initialize(
            mockFragmentManager,
            mockBottomNavigationView,
            testFragmentContainerId,
            mockLifecycleOwner
        )
        
        // Assert
        assertTrue("AppNavigator should be initialized", appNavigator.isInitialized())
    }
    
    @Test
    fun `navigation fails when not initialized`() {
        // Act & Assert
        assertFalse("navigateToCharge should fail when not initialized", appNavigator.navigateToCharge())
        assertFalse("navigateToDischarge should fail when not initialized", appNavigator.navigateToDischarge())
        assertFalse("navigateToHealth should fail when not initialized", appNavigator.navigateToHealth())
        assertFalse("navigateToOthers should fail when not initialized", appNavigator.navigateToOthers())
        assertFalse("navigateToAnimation should fail when not initialized", appNavigator.navigateToAnimation())
        assertFalse("navigateToSettings should fail when not initialized", appNavigator.navigateToSettings())
        assertFalse("navigateBack should fail when not initialized", appNavigator.navigateBack())
    }
    
    @Test
    fun `navigateToCharge uses dynamic navigation successfully`() = runTest {
        // Arrange
        initializeNavigator()
        val chargingStatus = CoreBatteryStatus.createDefault().copy(isCharging = true)
        every { mockCoreBatteryStatsProvider.getCurrentStatus() } returns chargingStatus
        every { mockDynamicNavigationManager.handleUserNavigation(R.id.chargeFragment) } returns true
        
        // Act
        val result = appNavigator.navigateToCharge()
        
        // Assert
        assertTrue("navigateToCharge should succeed", result)
        verify { mockDynamicNavigationManager.handleUserNavigation(R.id.chargeFragment) }
        verify { mockBottomNavigationView.selectedItemId = R.id.chargeFragment }
    }
    
    @Test
    fun `navigateToDischarge uses dynamic navigation successfully`() = runTest {
        // Arrange
        initializeNavigator()
        val dischargingStatus = CoreBatteryStatus.createDefault().copy(isCharging = false)
        every { mockCoreBatteryStatsProvider.getCurrentStatus() } returns dischargingStatus
        every { mockDynamicNavigationManager.handleUserNavigation(R.id.dischargeFragment) } returns true
        
        // Act
        val result = appNavigator.navigateToDischarge()
        
        // Assert
        assertTrue("navigateToDischarge should succeed", result)
        verify { mockDynamicNavigationManager.handleUserNavigation(R.id.dischargeFragment) }
        verify { mockBottomNavigationView.selectedItemId = R.id.dischargeFragment }
    }
    
    @Test
    fun `navigateToHealth uses standard navigation successfully`() = runTest {
        // Arrange
        initializeNavigator()
        
        // Act
        val result = appNavigator.navigateToHealth()
        
        // Assert
        assertTrue("navigateToHealth should succeed", result)
        verify { mockFragmentManager.beginTransaction() }
        verify { mockFragmentTransaction.replace(testFragmentContainerId, any<androidx.fragment.app.Fragment>(), any()) }
        verify { mockFragmentTransaction.addToBackStack(null) }
        verify { mockFragmentTransaction.commit() }
        verify { mockBottomNavigationView.selectedItemId = R.id.healthFragment }
    }
    
    @Test
    fun `navigateToOthers uses standard navigation successfully`() = runTest {
        // Arrange
        initializeNavigator()
        
        // Act
        val result = appNavigator.navigateToOthers()
        
        // Assert
        assertTrue("navigateToOthers should succeed", result)
        verify { mockFragmentManager.beginTransaction() }
        verify { mockFragmentTransaction.replace(testFragmentContainerId, any<androidx.fragment.app.Fragment>(), any()) }
        verify { mockFragmentTransaction.addToBackStack(null) }
        verify { mockFragmentTransaction.commit() }
        verify { mockBottomNavigationView.selectedItemId = R.id.othersFragment }
    }
    
    @Test
    fun `navigateToAnimation uses standard navigation successfully`() = runTest {
        // Arrange
        initializeNavigator()
        
        // Act
        val result = appNavigator.navigateToAnimation()
        
        // Assert
        assertTrue("navigateToAnimation should succeed", result)
        verify { mockFragmentManager.beginTransaction() }
        verify { mockFragmentTransaction.replace(testFragmentContainerId, any<androidx.fragment.app.Fragment>(), any()) }
        verify { mockFragmentTransaction.addToBackStack(null) }
        verify { mockFragmentTransaction.commit() }
        verify { mockBottomNavigationView.selectedItemId = R.id.animationGridFragment }
    }
    
    @Test
    fun `navigateToSettings uses standard navigation successfully`() = runTest {
        // Arrange
        initializeNavigator()
        
        // Act
        val result = appNavigator.navigateToSettings()
        
        // Assert
        assertTrue("navigateToSettings should succeed", result)
        verify { mockFragmentManager.beginTransaction() }
        verify { mockFragmentTransaction.replace(testFragmentContainerId, any<androidx.fragment.app.Fragment>(), any()) }
        verify { mockFragmentTransaction.addToBackStack(null) }
        verify { mockFragmentTransaction.commit() }
        verify { mockBottomNavigationView.selectedItemId = R.id.settingsFragment }
    }
    
    @Test
    fun `navigateBack pops back stack successfully`() = runTest {
        // Arrange
        initializeNavigator()
        every { mockFragmentManager.backStackEntryCount } returns 2
        
        // Act
        val result = appNavigator.navigateBack()
        
        // Assert
        assertTrue("navigateBack should succeed", result)
        verify { mockFragmentManager.popBackStack() }
    }
    
    @Test
    fun `navigateBack fails when no back stack entries`() = runTest {
        // Arrange
        initializeNavigator()
        every { mockFragmentManager.backStackEntryCount } returns 0
        
        // Act
        val result = appNavigator.navigateBack()
        
        // Assert
        assertFalse("navigateBack should fail when no back stack entries", result)
        verify(exactly = 0) { mockFragmentManager.popBackStack() }
    }
    
    @Test
    fun `dynamic navigation falls back to standard when DynamicNavigationManager fails`() = runTest {
        // Arrange
        initializeNavigator()
        val chargingStatus = CoreBatteryStatus.createDefault().copy(isCharging = true)
        every { mockCoreBatteryStatsProvider.getCurrentStatus() } returns chargingStatus
        every { mockDynamicNavigationManager.handleUserNavigation(R.id.chargeFragment) } returns false

        // Act
        val result = appNavigator.navigateToCharge()

        // Assert
        assertTrue("navigateToCharge should succeed with fallback", result)
        verify { mockDynamicNavigationManager.handleUserNavigation(R.id.chargeFragment) }
        verify { mockFragmentManager.beginTransaction() }
        verify { mockFragmentTransaction.replace(testFragmentContainerId, any<androidx.fragment.app.Fragment>()) }
        verify { mockFragmentTransaction.addToBackStack(null) }
        verify { mockFragmentTransaction.commit() }
    }

    @Test
    fun `standard navigation handles FragmentManager exceptions`() = runTest {
        // Arrange
        initializeNavigator()
        every { mockFragmentManager.beginTransaction() } throws RuntimeException("Fragment transaction failed")

        // Act
        val result = appNavigator.navigateToHealth()

        // Assert
        assertFalse("navigateToHealth should fail when FragmentManager throws exception", result)
    }

    @Test
    fun `battery state validation logs correct information for charge navigation`() = runTest {
        // Arrange
        initializeNavigator()
        val chargingStatus = CoreBatteryStatus.createDefault().copy(
            isCharging = true,
            percentage = 75
        )
        every { mockCoreBatteryStatsProvider.getCurrentStatus() } returns chargingStatus
        every { mockDynamicNavigationManager.handleUserNavigation(R.id.chargeFragment) } returns true

        // Act
        val result = appNavigator.navigateToCharge()

        // Assert
        assertTrue("navigateToCharge should succeed", result)
        verify { mockCoreBatteryStatsProvider.getCurrentStatus() }
    }

    @Test
    fun `battery state validation handles null battery status`() = runTest {
        // Arrange
        initializeNavigator()
        every { mockCoreBatteryStatsProvider.getCurrentStatus() } returns null
        every { mockDynamicNavigationManager.handleUserNavigation(R.id.dischargeFragment) } returns true

        // Act
        val result = appNavigator.navigateToDischarge()

        // Assert
        assertTrue("navigateToDischarge should succeed even with null battery status", result)
        verify { mockCoreBatteryStatsProvider.getCurrentStatus() }
    }

    @Test
    fun `performance stats track navigation attempts correctly`() = runTest {
        // Arrange
        initializeNavigator()
        every { mockDynamicNavigationManager.handleUserNavigation(any()) } returns true

        // Act - Perform multiple navigations
        appNavigator.navigateToCharge()
        appNavigator.navigateToDischarge()
        appNavigator.navigateToHealth()

        // Get performance stats
        val stats = appNavigator.getPerformanceStats()

        // Assert
        assertTrue("Stats should contain total navigations", stats.contains("Total Navigations: 3"))
        assertTrue("Stats should contain successful navigations", stats.contains("Successful: 3"))
        assertTrue("Stats should contain failed navigations", stats.contains("Failed: 0"))
        assertTrue("Stats should contain success rate", stats.contains("Success Rate: 100%"))
        assertTrue("Stats should show initialized status", stats.contains("Initialized: true"))
    }

    @Test
    fun `performance stats track failed navigation attempts`() = runTest {
        // Arrange
        initializeNavigator()
        every { mockDynamicNavigationManager.handleUserNavigation(any()) } returns false
        every { mockFragmentManager.beginTransaction() } throws RuntimeException("Transaction failed")

        // Act - Attempt navigations that will fail
        appNavigator.navigateToCharge() // Will fail in fallback
        appNavigator.navigateToHealth() // Will fail in standard navigation

        // Get performance stats
        val stats = appNavigator.getPerformanceStats()

        // Assert
        assertTrue("Stats should contain total navigations", stats.contains("Total Navigations: 2"))
        assertTrue("Stats should contain successful navigations", stats.contains("Successful: 0"))
        assertTrue("Stats should contain failed navigations", stats.contains("Failed: 2"))
        assertTrue("Stats should contain success rate", stats.contains("Success Rate: 0%"))
    }

    @Test
    fun `bottom navigation update handles exceptions gracefully`() = runTest {
        // Arrange
        initializeNavigator()
        every { mockBottomNavigationView.selectedItemId = any() } throws RuntimeException("Bottom nav update failed")

        // Act
        val result = appNavigator.navigateToHealth()

        // Assert
        assertTrue("Navigation should still succeed even if bottom nav update fails", result)
        verify { mockFragmentManager.beginTransaction() }
        verify { mockFragmentTransaction.commit() }
    }

    @Test
    fun `fallback navigation handles FragmentManager exceptions`() = runTest {
        // Arrange
        initializeNavigator()
        val chargingStatus = CoreBatteryStatus.createDefault().copy(isCharging = true)
        every { mockCoreBatteryStatsProvider.getCurrentStatus() } returns chargingStatus
        every { mockDynamicNavigationManager.handleUserNavigation(R.id.chargeFragment) } returns false
        every { mockFragmentManager.beginTransaction() } throws RuntimeException("Fallback transaction failed")

        // Act
        val result = appNavigator.navigateToCharge()

        // Assert
        assertFalse("navigateToCharge should fail when both dynamic and fallback fail", result)
    }

    /**
     * Helper method to initialize the navigator for tests.
     */
    private fun initializeNavigator() {
        appNavigator.initialize(
            mockFragmentManager,
            mockBottomNavigationView,
            testFragmentContainerId,
            mockLifecycleOwner
        )
    }
}
