package com.tqhit.battery.one.features.navigation

import android.util.Log
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages dynamic navigation based on real-time charging state.
 * This class coordinates fragment switching and navigation visibility
 * based on battery charging status from CoreBatteryStatsProvider.
 */
@Singleton
class DynamicNavigationManager @Inject constructor(
    private val coreBatteryStatsProvider: CoreBatteryStatsProvider
) {
    companion object {
        private const val TAG = "DynamicNavigationManager"
        private const val FRAGMENT_TAG_PREFIX = "nav_fragment_"
    }

    private val _navigationState = MutableStateFlow<NavigationState?>(null)
    val navigationState: StateFlow<NavigationState?> = _navigationState.asStateFlow()

    private val _stateChanges = MutableStateFlow<NavigationStateChange?>(null)
    val stateChanges: StateFlow<NavigationStateChange?> = _stateChanges.asStateFlow()

    private var isInitialized = false
    private var fragmentManager: FragmentManager? = null
    private var bottomNavigationView: BottomNavigationView? = null
    private var fragmentContainerId: Int = 0
    private var isUpdatingNavigation = false
    private var currentActiveFragment: Fragment? = null

    // Fragment cache for performance optimization
    private val fragmentCache = mutableMapOf<Int, Fragment>()

    // Fragment lifecycle optimizer
    private val lifecycleOptimizer = FragmentLifecycleOptimizer()

    // Performance tracking
    private var navigationStartTime: Long = 0
    private var fragmentCreationCount = 0
    private var cacheHitCount = 0
    private var useShowHidePattern = true // Flag to control navigation pattern
    
    /**
     * Initializes the navigation manager with required components.
     * Must be called before using other methods.
     *
     * @param fragmentManager The FragmentManager for fragment transactions
     * @param bottomNavigationView The BottomNavigationView to manage
     * @param fragmentContainerId The container ID for fragment replacement
     * @param lifecycleOwner The lifecycle owner for coroutine scope
     * @param restoredSelectedItemId The restored selected item ID from saved state (null for fresh start)
     */
    fun initialize(
        fragmentManager: FragmentManager,
        bottomNavigationView: BottomNavigationView,
        fragmentContainerId: Int,
        lifecycleOwner: LifecycleOwner,
        restoredSelectedItemId: Int? = null
    ) {
        if (isInitialized) {
            Log.w(TAG, "NAVIGATION_RESTORE: DynamicNavigationManager already initialized, reinitializing for new activity instance")
            // Reset state for new activity instance
            isInitialized = false
            _navigationState.value = null
            // Clear fragment cache on reinitialization to prevent memory leaks
            clearFragmentCache()
        }

        this.fragmentManager = fragmentManager
        this.bottomNavigationView = bottomNavigationView
        this.fragmentContainerId = fragmentContainerId

        Log.d(TAG, "NAVIGATION_RESTORE: Initializing DynamicNavigationManager with restored item: $restoredSelectedItemId")

        // Start monitoring battery status changes
        startBatteryStatusMonitoring(lifecycleOwner)

        // Set up initial state with restoration context
        setupInitialState(restoredSelectedItemId)

        isInitialized = true
        Log.d(TAG, "NAVIGATION_RESTORE: DynamicNavigationManager initialized successfully")
    }
    
    /**
     * Starts monitoring battery status changes and updates navigation accordingly.
     */
    private fun startBatteryStatusMonitoring(lifecycleOwner: LifecycleOwner) {
        lifecycleOwner.lifecycleScope.launch {
            coreBatteryStatsProvider.coreBatteryStatusFlow
                .filterNotNull()
                .map { it.isCharging }
                .distinctUntilChanged()
                .collect { isCharging ->
                    Log.d(TAG, "Battery charging state changed: $isCharging")
                    handleChargingStateChange(isCharging)
                }
        }
    }
    
    /**
     * Sets up the initial navigation state based on current battery status and restored state.
     *
     * @param restoredSelectedItemId The restored selected item ID from saved state (null for fresh start)
     */
    private fun setupInitialState(restoredSelectedItemId: Int? = null) {
        val currentBatteryStatus = coreBatteryStatsProvider.getCurrentStatus()
        val isCharging = currentBatteryStatus?.isCharging ?: false

        Log.d(TAG, "NAVIGATION_RESTORE: Setting up initial state - charging: $isCharging, restored item: $restoredSelectedItemId")

        val initialState = if (restoredSelectedItemId != null) {
            // State restoration - create state based on restored item
            Log.d(TAG, "NAVIGATION_RESTORE: Creating state for restoration")
            when (restoredSelectedItemId) {
                R.id.chargeFragment -> NavigationState.createChargingState(shouldShowTransition = false)
                R.id.dischargeFragment -> NavigationState.createDischargingState(shouldShowTransition = false)
                R.id.animationGridFragment -> NavigationState.createAnimationState(isCharging, shouldShowTransition = false)
                R.id.healthFragment -> NavigationState.createAnimationState(isCharging, shouldShowTransition = false).copy(
                    activeFragmentId = R.id.healthFragment
                )
                R.id.settingsFragment -> NavigationState.createAnimationState(isCharging, shouldShowTransition = false).copy(
                    activeFragmentId = R.id.settingsFragment
                )
                else -> {
                    Log.w(TAG, "NAVIGATION_RESTORE: Unknown restored item $restoredSelectedItemId, falling back to animation state")
                    NavigationState.createAnimationState(isCharging, shouldShowTransition = false)
                }
            }
        } else {
            // Fresh start - use animation state as default
            Log.d(TAG, "NAVIGATION_RESTORE: Creating fresh animation state")
            NavigationState.createAnimationState(isCharging, shouldShowTransition = false)
        }

        updateNavigationState(initialState, StateChangeReason.INITIAL_SETUP)
    }
    
    /**
     * Handles charging state changes and updates navigation accordingly.
     * Implements dynamic fragment switching for charge/discharge fragments.
     */
    private fun handleChargingStateChange(isCharging: Boolean) {
        val currentState = _navigationState.value

        // Don't update if the state hasn't actually changed
        if (currentState?.isCharging == isCharging) {
            Log.v(TAG, "DYNAMIC_SWITCHING: Charging state unchanged, skipping update")
            return
        }

        // ENHANCED_DEBUG_LOGGING: Battery state change tracking
        Log.d(TAG, "BATTERY_STATE_CHANGE: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "BATTERY_STATE_CHANGE: 🔋 BATTERY STATE CHANGE DETECTED")
        Log.d(TAG, "BATTERY_STATE_CHANGE: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "BATTERY_STATE_CHANGE: Previous state: ${currentState?.isCharging ?: "unknown"}")
        Log.d(TAG, "BATTERY_STATE_CHANGE: New state: $isCharging")
        Log.d(TAG, "BATTERY_STATE_CHANGE: Current fragment: ${currentState?.activeFragmentId?.let { getFragmentName(it) } ?: "none"}")
        Log.d(TAG, "BATTERY_STATE_CHANGE: Timestamp: ${System.currentTimeMillis()}")

        // Determine if we need dynamic fragment switching
        val shouldSwitchFragment = shouldPerformDynamicFragmentSwitch(currentState, isCharging)
        Log.d(TAG, "BATTERY_STATE_CHANGE: Should perform dynamic switch: $shouldSwitchFragment")

        val newState = if (shouldSwitchFragment) {
            // User is on charge/discharge fragment - switch to the appropriate one
            Log.d(TAG, "BATTERY_STATE_CHANGE: 🔄 Performing dynamic fragment switch")
            createDynamicSwitchState(currentState, isCharging)
        } else {
            // User is on other fragments - update charging state but keep current fragment
            Log.d(TAG, "BATTERY_STATE_CHANGE: 📝 Updating charging state without fragment switch")
            currentState?.copy(isCharging = isCharging) ?: if (isCharging) {
                NavigationState.createChargingState()
            } else {
                NavigationState.createDischargingState()
            }
        }

        Log.d(TAG, "BATTERY_STATE_CHANGE: New navigation state: activeFragment=${getFragmentName(newState.activeFragmentId)}, charging=${newState.isCharging}")
        Log.d(TAG, "BATTERY_STATE_CHANGE: ═══════════════════════════════════════════════════════════")

        val reason = if (isCharging) StateChangeReason.CHARGING_STARTED else StateChangeReason.CHARGING_STOPPED

        if (shouldSwitchFragment) {
            Log.i(TAG, "DYNAMIC_SWITCHING: Performing automatic fragment switch from ${getFragmentName(currentState?.activeFragmentId)} to ${getFragmentName(newState.activeFragmentId)}")
        } else {
            Log.d(TAG, "DYNAMIC_SWITCHING: Updating charging state without fragment switch - staying on ${getFragmentName(currentState?.activeFragmentId)}")
        }

        updateNavigationState(newState, reason)
    }

    /**
     * Determines if dynamic fragment switching should be performed based on current state.
     */
    private fun shouldPerformDynamicFragmentSwitch(currentState: NavigationState?, isCharging: Boolean): Boolean {
        if (currentState == null) {
            Log.w(TAG, "DYNAMIC_SWITCHING: No current state available for switch analysis")
            return false
        }

        val currentFragmentId = currentState.activeFragmentId
        val currentFragmentName = getFragmentName(currentFragmentId)

        // Only switch if user is currently on charge or discharge fragment
        val isOnChargeDischargeFragment = currentFragmentId == R.id.chargeFragment || currentFragmentId == R.id.dischargeFragment

        Log.d(TAG, "DYNAMIC_SWITCHING: Switch analysis - currentFragment: $currentFragmentName, isOnChargeDischarge: $isOnChargeDischargeFragment")

        if (!isOnChargeDischargeFragment) {
            Log.v(TAG, "DYNAMIC_SWITCHING: User not on charge/discharge fragment ($currentFragmentName), no switch needed")
            return false
        }

        // Check if the current fragment matches the new charging state
        val shouldBeOnChargeFragment = isCharging
        val isCurrentlyOnChargeFragment = currentFragmentId == R.id.chargeFragment

        val needsSwitch = shouldBeOnChargeFragment != isCurrentlyOnChargeFragment

        Log.i(TAG, "DYNAMIC_SWITCHING: Detailed analysis:")
        Log.i(TAG, "DYNAMIC_SWITCHING:   - Current fragment: $currentFragmentName (ID: $currentFragmentId)")
        Log.i(TAG, "DYNAMIC_SWITCHING:   - New charging state: $isCharging")
        Log.i(TAG, "DYNAMIC_SWITCHING:   - Should be on charge fragment: $shouldBeOnChargeFragment")
        Log.i(TAG, "DYNAMIC_SWITCHING:   - Currently on charge fragment: $isCurrentlyOnChargeFragment")
        Log.i(TAG, "DYNAMIC_SWITCHING:   - Needs switch: $needsSwitch")

        return needsSwitch
    }

    /**
     * Creates a new navigation state for dynamic fragment switching.
     */
    private fun createDynamicSwitchState(currentState: NavigationState?, isCharging: Boolean): NavigationState {
        val targetFragmentId = if (isCharging) R.id.chargeFragment else R.id.dischargeFragment

        return NavigationState(
            activeFragmentId = targetFragmentId,
            visibleMenuItems = currentState?.visibleMenuItems ?: NavigationState.ALWAYS_VISIBLE_ITEMS,
            isCharging = isCharging,
            shouldShowTransition = true // Always show transition for dynamic switching
        )
    }

    /**
     * Gets a human-readable fragment name for logging.
     */
    private fun getFragmentName(fragmentId: Int?): String {
        return when (fragmentId) {
            R.id.chargeFragment -> "ChargeFragment"
            R.id.dischargeFragment -> "DischargeFragment"
            R.id.animationGridFragment -> "AnimationGridFragment"
            R.id.othersFragment -> "OthersFragment"
            R.id.healthFragment -> "HealthFragment"
            R.id.settingsFragment -> "SettingsFragment"
            else -> "Unknown($fragmentId)"
        }
    }

    /**
     * NAVIGATION_STATE_FIX: Gets the fragment ID from a fragment instance.
     * Used for state synchronization.
     */
    private fun getFragmentId(fragment: Fragment): Int? {
        return when (fragment) {
            is com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment -> R.id.animationGridFragment
            is com.tqhit.battery.one.fragment.main.others.OthersFragment -> R.id.othersFragment
            is com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment -> R.id.chargeFragment
            is com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment -> R.id.dischargeFragment
            is com.tqhit.battery.one.fragment.main.HealthFragment -> R.id.healthFragment
            is com.tqhit.battery.one.fragment.main.SettingsFragment -> R.id.settingsFragment
            else -> {
                Log.w(TAG, "NAVIGATION_STATE_FIX: Unknown fragment type: ${fragment.javaClass.simpleName}")
                null
            }
        }
    }
    
    /**
     * Updates the navigation state and applies changes to UI.
     */
    private fun updateNavigationState(newState: NavigationState, reason: StateChangeReason) {
        val previousState = _navigationState.value
        _navigationState.value = newState

        // Enhanced logging for dynamic switching
        val previousFragmentName = getFragmentName(previousState?.activeFragmentId)
        val newFragmentName = getFragmentName(newState.activeFragmentId)
        val isDynamicSwitch = reason == StateChangeReason.CHARGING_STARTED || reason == StateChangeReason.CHARGING_STOPPED

        if (isDynamicSwitch && previousState?.activeFragmentId != newState.activeFragmentId) {
            Log.i(TAG, "DYNAMIC_SWITCHING: State transition - $previousFragmentName → $newFragmentName (reason: $reason, charging: ${newState.isCharging})")
        } else {
            Log.d(TAG, "DYNAMIC_SWITCHING: Navigation state updated - ${newFragmentName} (charging: ${newState.isCharging}, reason: $reason)")
        }

        // Emit state change event
        val stateChange = NavigationStateChange(previousState, newState, reason)
        _stateChanges.value = stateChange

        // Apply changes to UI
        applyNavigationChanges(newState, previousState)
    }
    
    /**
     * Applies navigation changes to the UI components.
     */
    private fun applyNavigationChanges(newState: NavigationState, previousState: NavigationState?) {
        try {
            // Update fragment if needed
            if (previousState?.activeFragmentId != newState.activeFragmentId) {
                switchToFragment(newState)
            }
            
            // Update bottom navigation visibility and selection
            updateBottomNavigation(newState)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error applying navigation changes", e)
        }
    }
    
    /**
     * Switches to the fragment specified in the navigation state using optimized caching.
     */
    private fun switchToFragment(state: NavigationState) {
        navigationStartTime = System.currentTimeMillis()

        val fragmentManager = this.fragmentManager ?: run {
            Log.e(TAG, "DYNAMIC_SWITCHING: FragmentManager not available for fragment switch")
            return
        }

        val targetFragmentName = getFragmentName(state.activeFragmentId)
        val currentFragmentName = getFragmentName(currentActiveFragment?.let { getFragmentId(it) })

        Log.d(TAG, "DYNAMIC_SWITCHING: Switching from $currentFragmentName to $targetFragmentName")

        try {
            if (useShowHidePattern) {
                performShowHideNavigation(state, fragmentManager)
            } else {
                performReplaceNavigation(state, fragmentManager)
            }

            val navigationTime = System.currentTimeMillis() - navigationStartTime
            Log.d(TAG, "DYNAMIC_SWITCHING: Fragment switch completed in ${navigationTime}ms (cache hits: $cacheHitCount, creations: $fragmentCreationCount)")

        } catch (e: Exception) {
            Log.e(TAG, "DYNAMIC_SWITCHING: Error switching fragment", e)
            // Fallback to replace pattern if show/hide fails
            if (useShowHidePattern) {
                Log.w(TAG, "DYNAMIC_SWITCHING: Show/hide pattern failed, falling back to replace pattern")
                useShowHidePattern = false
                try {
                    performReplaceNavigation(state, fragmentManager)
                } catch (fallbackException: Exception) {
                    Log.e(TAG, "DYNAMIC_SWITCHING: Fallback replace pattern also failed", fallbackException)
                }
            }
        }
    }


    
    /**
     * Updates the bottom navigation view based on the navigation state.
     */
    private fun updateBottomNavigation(state: NavigationState) {
        val bottomNav = this.bottomNavigationView ?: run {
            Log.e(TAG, "NAVIGATION_RESTORE: BottomNavigationView not available")
            return
        }

        try {
            // Prevent infinite loop by setting flag
            isUpdatingNavigation = true

            // Update selected item
            bottomNav.selectedItemId = state.activeFragmentId
            Log.d(TAG, "NAVIGATION_RESTORE: Set selected item to: ${state.activeFragmentId}")

            // Update menu item visibility
            val menu = bottomNav.menu
            val visibleItemsCount = state.visibleMenuItems.size
            Log.d(TAG, "NAVIGATION_RESTORE: Updating menu visibility - total items: ${menu.size()}, visible items: $visibleItemsCount")

            for (i in 0 until menu.size()) {
                val menuItem = menu.getItem(i)
                val shouldBeVisible = state.isMenuItemVisible(menuItem.itemId)
                menuItem.isVisible = shouldBeVisible

                Log.d(TAG, "NAVIGATION_RESTORE: Menu item ${menuItem.itemId} visibility: $shouldBeVisible")
            }

            // Validate final state
            val actualVisibleCount = (0 until menu.size()).count { menu.getItem(it).isVisible }
            Log.d(TAG, "NAVIGATION_RESTORE: Final visible menu items count: $actualVisibleCount")

        } catch (e: Exception) {
            Log.e(TAG, "Error updating bottom navigation", e)
        } finally {
            // Reset flag
            isUpdatingNavigation = false
        }
    }
    
    /**
     * Handles user navigation selection.
     * Returns true if the navigation was handled, false otherwise.
     */
    fun handleUserNavigation(itemId: Int): Boolean {
        Log.d(TAG, "NAVIGATION_INVESTIGATION: === USER NAVIGATION REQUEST START ===")
        Log.d(TAG, "MultiNavigation: Navigation request to fragment ID: $itemId")

        // Log fragment cache state before navigation
        logFragmentCacheState("BEFORE_NAVIGATION")

        if (!isInitialized) {
            Log.w(TAG, "NAVIGATION_INVESTIGATION: ❌ Navigation manager not initialized")
            Log.w(TAG, "FragmentCache: Navigation manager not initialized - cache state unknown")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: === USER NAVIGATION FAILED (NOT INITIALIZED) ===")
            return false
        }

        // Prevent infinite loop - ignore navigation events triggered by our own updates
        if (isUpdatingNavigation) {
            Log.v(TAG, "NAVIGATION_INVESTIGATION: Ignoring navigation event during update (preventing infinite loop)")
            return true
        }

        // NAVIGATION_STATE_FIX: Perform state synchronization before processing navigation
        performStateSynchronizationCheck()

        val currentState = _navigationState.value ?: run {
            Log.e(TAG, "NAVIGATION_INVESTIGATION: ❌ No current navigation state available")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: === USER NAVIGATION FAILED (NO STATE) ===")
            return false
        }

        val currentFragmentName = getFragmentName(currentState.activeFragmentId)
        val targetFragmentName = getFragmentName(itemId)

        // ENHANCED_DEBUG_LOGGING: Comprehensive navigation flow tracking
        Log.d(TAG, "NAVIGATION_FLOW: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "NAVIGATION_FLOW: 🧭 NAVIGATION REQUEST ANALYSIS")
        Log.d(TAG, "NAVIGATION_FLOW: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "NAVIGATION_FLOW: From: $currentFragmentName (ID: ${currentState.activeFragmentId})")
        Log.d(TAG, "NAVIGATION_FLOW: To: $targetFragmentName (ID: $itemId)")
        Log.d(TAG, "NAVIGATION_FLOW: Current charging state: ${currentState.isCharging}")
        Log.d(TAG, "NAVIGATION_FLOW: Fragment cache size: ${fragmentCache.size}")
        Log.d(TAG, "NAVIGATION_FLOW: Back stack entry count: ${fragmentManager?.backStackEntryCount ?: "N/A"}")
        Log.d(TAG, "NAVIGATION_FLOW: Current active fragment: ${currentActiveFragment?.javaClass?.simpleName ?: "none"}")
        Log.d(TAG, "NAVIGATION_FLOW: Navigation timestamp: ${System.currentTimeMillis()}")

        // ENHANCED_DEBUG_LOGGING: Fragment cache state with detailed visibility info
        Log.d(TAG, "NAVIGATION_FLOW: ─── Fragment Cache State ───")
        if (fragmentCache.isEmpty()) {
            Log.d(TAG, "NAVIGATION_FLOW: Cache is empty")
        } else {
            fragmentCache.forEach { (id, fragment) ->
                val fragmentName = getFragmentName(id)
                val visibilityState = when {
                    fragment.isVisible -> "VISIBLE"
                    fragment.isHidden -> "HIDDEN"
                    !fragment.isAdded -> "NOT_ADDED"
                    fragment.isDetached -> "DETACHED"
                    fragment.isRemoving -> "REMOVING"
                    else -> "UNKNOWN"
                }
                Log.d(TAG, "NAVIGATION_FLOW: 📦 $fragmentName: $visibilityState (added=${fragment.isAdded})")
            }
        }
        Log.d(TAG, "NAVIGATION_FLOW: ─────────────────────────────")

        // Special handling for charge/discharge fragments - allow navigation even if not in visible menu
        val isChargeDischargeFragment = itemId == R.id.chargeFragment || itemId == R.id.dischargeFragment

        // Check if the selected item is visible in current state (with exception for charge/discharge fragments)
        if (!currentState.isMenuItemVisible(itemId) && !isChargeDischargeFragment) {
            Log.w(TAG, "NAVIGATION_INVESTIGATION: ❌ User tried to navigate to hidden item: $itemId")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: === USER NAVIGATION FAILED (HIDDEN ITEM) ===")
            return false
        }

        if (isChargeDischargeFragment) {
            Log.i(TAG, "NAVIGATION_INVESTIGATION: 🔋 CHARGE/DISCHARGE NAVIGATION DETECTED")
            Log.i(TAG, "NAVIGATION_INVESTIGATION: Allowing navigation to charge/discharge fragment: $targetFragmentName (not in visible menu but allowed for dynamic switching)")
            Log.i(TAG, "NAVIGATION_INVESTIGATION: Navigation from Others: ${currentState.activeFragmentId == R.id.othersFragment}")
        }

        // Don't update if already on the same fragment
        if (currentState.activeFragmentId == itemId) {
            Log.v(TAG, "NAVIGATION_INVESTIGATION: Already on fragment $targetFragmentName, skipping update")
            return true
        }

        // Special handling for charge/discharge fragments to ensure proper state synchronization
        val isNavigatingToChargeDischarge = itemId == R.id.chargeFragment || itemId == R.id.dischargeFragment
        if (isNavigatingToChargeDischarge) {
            Log.i(TAG, "NAVIGATION_INVESTIGATION: 🔋 PROCESSING CHARGE/DISCHARGE NAVIGATION")
            Log.i(TAG, "NAVIGATION_INVESTIGATION: User navigating to charge/discharge fragment - $targetFragmentName")

            // Ensure the charging state in navigation state matches the target fragment
            val shouldBeCharging = itemId == R.id.chargeFragment
            val currentBatteryStatus = coreBatteryStatsProvider.getCurrentStatus()
            val actualIsCharging = currentBatteryStatus?.isCharging ?: false

            Log.d(TAG, "NAVIGATION_INVESTIGATION: Battery state analysis:")
            Log.d(TAG, "NAVIGATION_INVESTIGATION:   - Target fragment should be charging: $shouldBeCharging")
            Log.d(TAG, "NAVIGATION_INVESTIGATION:   - Actual battery charging state: $actualIsCharging")
            Log.d(TAG, "NAVIGATION_INVESTIGATION:   - Battery status available: ${currentBatteryStatus != null}")
            if (currentBatteryStatus != null) {
                Log.d(TAG, "NAVIGATION_INVESTIGATION:   - Battery percentage: ${currentBatteryStatus.percentage}%")
            }

            // Create new state with proper charging state synchronization
            val newState = currentState.copy(
                activeFragmentId = itemId,
                isCharging = actualIsCharging, // Use actual battery status
                shouldShowTransition = true
            )

            Log.i(TAG, "NAVIGATION_INVESTIGATION: ✅ Synchronized navigation state - fragment: $targetFragmentName, charging: $actualIsCharging")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: Calling updateNavigationState for charge/discharge navigation...")

            try {
                updateNavigationState(newState, StateChangeReason.USER_NAVIGATION)
                Log.d(TAG, "NAVIGATION_INVESTIGATION: ✅ updateNavigationState completed successfully")

                // Log fragment cache state after navigation
                logFragmentCacheState("AFTER_CHARGE_DISCHARGE_NAVIGATION")

                Log.d(TAG, "NAVIGATION_INVESTIGATION: === USER NAVIGATION SUCCESS (CHARGE/DISCHARGE) ===")
                return true
            } catch (e: Exception) {
                Log.e(TAG, "NAVIGATION_INVESTIGATION: ❌ Exception in updateNavigationState for charge/discharge", e)
                Log.e(TAG, "FragmentCache: Exception during charge/discharge navigation - cache state may be corrupted")
                Log.d(TAG, "NAVIGATION_INVESTIGATION: === USER NAVIGATION FAILED (CHARGE/DISCHARGE EXCEPTION) ===")
                return false
            }
        }

        // Create new state for user navigation (non-charge/discharge fragments)
        val newState = currentState.copy(
            activeFragmentId = itemId,
            shouldShowTransition = true
        )

        Log.d(TAG, "NAVIGATION_INVESTIGATION: 📱 STANDARD NAVIGATION")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Standard user navigation - $targetFragmentName")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Calling updateNavigationState for standard navigation...")

        try {
            updateNavigationState(newState, StateChangeReason.USER_NAVIGATION)
            Log.d(TAG, "NAVIGATION_INVESTIGATION: ✅ updateNavigationState completed successfully")

            // Log fragment cache state after navigation
            logFragmentCacheState("AFTER_STANDARD_NAVIGATION")

            Log.d(TAG, "NAVIGATION_INVESTIGATION: === USER NAVIGATION SUCCESS (STANDARD) ===")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "NAVIGATION_INVESTIGATION: ❌ Exception in updateNavigationState for standard navigation", e)
            Log.e(TAG, "FragmentCache: Exception during standard navigation - cache state may be corrupted")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: === USER NAVIGATION FAILED (STANDARD EXCEPTION) ===")
            return false
        }
    }
    
    /**
     * Gets the current navigation state.
     */
    fun getCurrentState(): NavigationState? = _navigationState.value

    /**
     * Manually triggers dynamic switching for testing purposes.
     * This method can be used to test the dynamic switching logic.
     */
    fun triggerDynamicSwitchingTest(newChargingState: Boolean) {
        Log.i(TAG, "DYNAMIC_SWITCHING: Manual trigger test - newChargingState: $newChargingState")
        handleChargingStateChange(newChargingState)
    }
    
    /**
     * Performs navigation using show/hide pattern for better performance.
     */
    private fun performShowHideNavigation(state: NavigationState, fragmentManager: FragmentManager) {
        val fragment = getOrCreateFragment(state.activeFragmentId)
        val fragmentTag = getFragmentTag(state.activeFragmentId)

        val isDynamicSwitch = (state.activeFragmentId == R.id.chargeFragment || state.activeFragmentId == R.id.dischargeFragment) &&
                             (currentActiveFragment?.let { getFragmentId(it) } == R.id.chargeFragment ||
                              currentActiveFragment?.let { getFragmentId(it) } == R.id.dischargeFragment)

        if (isDynamicSwitch) {
            Log.i(TAG, "DYNAMIC_SWITCHING: Show/Hide navigation for dynamic switch to: ${fragment.javaClass.simpleName} (cached: ${fragmentCache.containsKey(state.activeFragmentId)})")
        } else {
            Log.d(TAG, "FRAGMENT_PERFORMANCE: Show/Hide navigation to: ${fragment.javaClass.simpleName} (cached: ${fragmentCache.containsKey(state.activeFragmentId)})")
        }

        val transaction = fragmentManager.beginTransaction()

        if (state.shouldShowTransition) {
            transaction.setCustomAnimations(
                android.R.anim.fade_in,
                android.R.anim.fade_out
            )
        }

        // Hide current active fragment if it exists
        currentActiveFragment?.let { activeFragment ->
            if (activeFragment.isAdded && activeFragment != fragment) {
                transaction.hide(activeFragment)
                Log.d(TAG, "FRAGMENT_PERFORMANCE: Hiding previous fragment: ${activeFragment.javaClass.simpleName} (isVisible before hide: ${activeFragment.isVisible})")
            }
        }

        // Additionally, hide ALL other fragments to ensure no overlays (especially AnimationGridFragment)
        fragmentCache.values.forEach { cachedFragment ->
            if (cachedFragment != fragment && cachedFragment.isAdded && cachedFragment.isVisible) {
                transaction.hide(cachedFragment)
                Log.d(TAG, "FRAGMENT_PERFORMANCE: Force hiding cached fragment: ${cachedFragment.javaClass.simpleName} to prevent overlay")
            }
        }

        // Check if this navigation is coming from Others Fragment
        val isFromOthersFragment = currentActiveFragment?.let { getFragmentId(it) } == R.id.othersFragment
        val isNavigatingToChargeDischarge = state.activeFragmentId == R.id.chargeFragment || state.activeFragmentId == R.id.dischargeFragment

        // Show or add the target fragment
        if (fragment.isAdded) {
            transaction.show(fragment)
            Log.d(TAG, "FRAGMENT_PERFORMANCE: Showing cached fragment: ${fragment.javaClass.simpleName}")

            // BACK_NAVIGATION_BLANK_FIX: Special handling for Others Fragment restoration
            if (fragment is com.tqhit.battery.one.fragment.main.others.OthersFragment) {
                Log.d(TAG, "BACK_NAVIGATION_BLANK_FIX: Showing cached Others Fragment - will trigger refresh in onResume")
            }
        } else {
            transaction.add(fragmentContainerId, fragment, fragmentTag)
            lifecycleOptimizer.registerFragment(fragment)
            Log.d(TAG, "FRAGMENT_PERFORMANCE: Adding new fragment: ${fragment.javaClass.simpleName}")
        }

        // ENHANCED_DEBUG_LOGGING: Back stack management analysis
        Log.d(TAG, "BACK_STACK_MGMT: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "BACK_STACK_MGMT: 🔙 BACK STACK MANAGEMENT ANALYSIS")
        Log.d(TAG, "BACK_STACK_MGMT: ═══════════════════════════════════════════════════════════")

        // BACK_NAVIGATION_FIX: Simplified and improved back stack management
        // Only add to back stack when navigating FROM Others TO Charge/Discharge fragments
        // This ensures proper back navigation behavior
        val shouldAddToBackStack = isFromOthersFragment && isNavigatingToChargeDischarge

        Log.d(TAG, "BACK_STACK_MGMT: Navigation pattern analysis:")
        Log.d(TAG, "BACK_STACK_MGMT:   📍 From Others Fragment: $isFromOthersFragment")
        Log.d(TAG, "BACK_STACK_MGMT:   📍 To Charge/Discharge: $isNavigatingToChargeDischarge")
        Log.d(TAG, "BACK_STACK_MGMT:   📍 Should add to back stack: $shouldAddToBackStack")
        Log.d(TAG, "BACK_STACK_MGMT:   📍 Current fragment: ${currentActiveFragment?.javaClass?.simpleName}")
        Log.d(TAG, "BACK_STACK_MGMT:   📍 Target fragment: ${fragment.javaClass.simpleName}")
        Log.d(TAG, "BACK_STACK_MGMT:   📍 Current back stack count: ${fragmentManager.backStackEntryCount}")

        if (shouldAddToBackStack) {
            // Add a unique back stack entry for Others -> Charge/Discharge navigation
            val backStackName = "others_to_${getFragmentName(state.activeFragmentId).lowercase()}"
            transaction.addToBackStack(backStackName)
            Log.d(TAG, "BACK_STACK_MGMT: ✅ Added '$backStackName' to back stack for proper back navigation")
            Log.d(TAG, "BACK_STACK_MGMT: 🎯 This enables Android back button to return to Others Fragment")

            // Use commit() instead of commitNow() when adding to back stack
            // This allows Android's back button to work properly
            transaction.commit()

            // Ensure transaction is executed
            fragmentManager.executePendingTransactions()
            Log.d(TAG, "BACK_STACK_MGMT: Transaction committed and executed")
            Log.d(TAG, "BACK_STACK_MGMT: New back stack count: ${fragmentManager.backStackEntryCount}")
        } else {
            // For all other navigation (including back navigation), use commitNow()
            // This ensures immediate execution without interfering with back stack
            Log.d(TAG, "BACK_STACK_MGMT: ⚡ Using commitNow() for non-back-stack navigation")
            Log.d(TAG, "BACK_STACK_MGMT: 📝 This preserves existing back stack structure")
            transaction.commitNow()
        }

        Log.d(TAG, "BACK_STACK_MGMT: ═══════════════════════════════════════════════════════════")

        // Verify the fragment is actually visible after commit
        val isActuallyVisible = fragment.isVisible
        Log.d(TAG, "FRAGMENT_PERFORMANCE: Fragment ${fragment.javaClass.simpleName} visibility after commit: $isActuallyVisible")

        // If fragment is not visible after commit, force it to be visible
        if (!isActuallyVisible && fragment.isAdded) {
            Log.w(TAG, "FRAGMENT_PERFORMANCE: Fragment not visible after commit, forcing visibility")
            val forceTransaction = fragmentManager.beginTransaction()
            forceTransaction.show(fragment)
            forceTransaction.commitNow()
            Log.d(TAG, "FRAGMENT_PERFORMANCE: Force visibility result: ${fragment.isVisible}")
        }

        // Update lifecycle state AFTER transaction is committed and visibility is confirmed
        currentActiveFragment?.let { previousFragment ->
            if (previousFragment != fragment && previousFragment.isAdded) {
                lifecycleOptimizer.onFragmentHidden(previousFragment)
                Log.d(TAG, "FRAGMENT_LIFECYCLE: Notified lifecycle optimizer that ${previousFragment.javaClass.simpleName} is hidden")
            }
        }

        // Also notify lifecycle optimizer for all other hidden fragments
        fragmentCache.values.forEach { cachedFragment ->
            if (cachedFragment != fragment && cachedFragment.isAdded && !cachedFragment.isVisible) {
                lifecycleOptimizer.onFragmentHidden(cachedFragment)
                Log.d(TAG, "FRAGMENT_LIFECYCLE: Ensured ${cachedFragment.javaClass.simpleName} is marked as hidden in lifecycle optimizer")
            }
        }

        // Update current active fragment reference and notify lifecycle optimizer
        currentActiveFragment = fragment
        lifecycleOptimizer.onFragmentVisible(fragment)

        // Validate fragment visibility to ensure no overlapping
        validateFragmentVisibility()

        // Additional check specifically for AnimationGridFragment overlay issues
        fixAnimationGridFragmentOverlay(fragment)

        // Verify fragment visibility state with detailed logging
        val visibleFragments = fragmentCache.values.filter { it.isVisible }
        val hiddenFragments = fragmentCache.values.filter { it.isHidden }

        Log.d(TAG, "FRAGMENT_VISIBILITY: Visible fragments: ${visibleFragments.size}, Hidden fragments: ${hiddenFragments.size}, Target: ${fragment.javaClass.simpleName} visible=${fragment.isVisible}")

        // Log each fragment's visibility state for debugging
        fragmentCache.values.forEach { cachedFragment ->
            Log.d(TAG, "FRAGMENT_VISIBILITY: ${cachedFragment.javaClass.simpleName} - isVisible: ${cachedFragment.isVisible}, isHidden: ${cachedFragment.isHidden}, isAdded: ${cachedFragment.isAdded}")
        }
    }

    /**
     * Performs navigation using replace pattern as fallback.
     */
    private fun performReplaceNavigation(state: NavigationState, fragmentManager: FragmentManager) {
        val fragment = getOrCreateFragment(state.activeFragmentId)

        val isDynamicSwitch = (state.activeFragmentId == R.id.chargeFragment || state.activeFragmentId == R.id.dischargeFragment) &&
                             (currentActiveFragment?.let { getFragmentId(it) } == R.id.chargeFragment ||
                              currentActiveFragment?.let { getFragmentId(it) } == R.id.dischargeFragment)

        // Check if this navigation is coming from Others Fragment
        val isFromOthersFragment = currentActiveFragment?.let { getFragmentId(it) } == R.id.othersFragment
        val isNavigatingToChargeDischarge = state.activeFragmentId == R.id.chargeFragment || state.activeFragmentId == R.id.dischargeFragment

        if (isDynamicSwitch) {
            Log.i(TAG, "DYNAMIC_SWITCHING: Replace navigation for dynamic switch to: ${fragment.javaClass.simpleName} (cached: ${fragmentCache.containsKey(state.activeFragmentId)})")
        } else {
            Log.d(TAG, "FRAGMENT_PERFORMANCE: Replace navigation to: ${fragment.javaClass.simpleName} (cached: ${fragmentCache.containsKey(state.activeFragmentId)})")
        }

        val transaction = fragmentManager.beginTransaction()

        if (state.shouldShowTransition) {
            transaction.setCustomAnimations(
                android.R.anim.fade_in,
                android.R.anim.fade_out
            )
        }

        transaction.replace(fragmentContainerId, fragment)

        // Note: Back stack logic is handled in show/hide navigation path only
        // to avoid duplicate back stack additions when falling back to replace

        transaction.commitAllowingStateLoss()

        // Update current active fragment reference
        currentActiveFragment = fragment
        lifecycleOptimizer.onFragmentVisible(fragment)

        if (isDynamicSwitch) {
            Log.i(TAG, "DYNAMIC_SWITCHING: Replace navigation completed for dynamic switch: ${fragment.javaClass.simpleName}")
        } else {
            Log.d(TAG, "FRAGMENT_PERFORMANCE: Replace navigation completed for: ${fragment.javaClass.simpleName}")
        }
    }

    /**
     * Checks if the navigation manager is initialized.
     */
    fun isInitialized(): Boolean = isInitialized

    /**
     * BACK_NAVIGATION_FIX: Handles back navigation state recovery.
     * Called when navigating back to ensure proper fragment state restoration.
     */
    fun handleBackNavigationStateRecovery() {
        Log.d(TAG, "NAVIGATION_STATE_FIX: === BACK NAVIGATION STATE RECOVERY START ===")

        try {
            // NAVIGATION_STATE_FIX: Detect actual visible fragment and sync state
            val actualVisibleFragment = detectActualVisibleFragment()
            val currentState = getCurrentState()

            Log.d(TAG, "NAVIGATION_STATE_FIX: Current state says active: ${getFragmentName(currentState?.activeFragmentId)}")
            Log.d(TAG, "NAVIGATION_STATE_FIX: Actually visible fragment: ${actualVisibleFragment?.javaClass?.simpleName}")

            if (actualVisibleFragment != null && currentState != null) {
                val actualFragmentId = getFragmentId(actualVisibleFragment)

                if (actualFragmentId != null && actualFragmentId != currentState.activeFragmentId) {
                    Log.w(TAG, "NAVIGATION_STATE_FIX: ⚠️ STATE DESYNC DETECTED!")
                    Log.w(TAG, "NAVIGATION_STATE_FIX: State says: ${getFragmentName(currentState.activeFragmentId)}")
                    Log.w(TAG, "NAVIGATION_STATE_FIX: Actually visible: ${getFragmentName(actualFragmentId)}")

                    // NAVIGATION_STATE_FIX: Force state synchronization
                    val correctedState = currentState.copy(activeFragmentId = actualFragmentId)
                    _navigationState.value = correctedState
                    currentActiveFragment = actualVisibleFragment

                    Log.i(TAG, "NAVIGATION_STATE_FIX: ✅ State synchronized to match visible fragment: ${getFragmentName(actualFragmentId)}")

                    // Update bottom navigation to match corrected state
                    bottomNavigationView?.selectedItemId = actualFragmentId

                } else {
                    Log.d(TAG, "NAVIGATION_STATE_FIX: State is already synchronized")
                }

                // If we're back to Others fragment, ensure it's properly refreshed
                if (actualFragmentId == R.id.othersFragment) {
                    val othersFragment = actualVisibleFragment as? com.tqhit.battery.one.fragment.main.others.OthersFragment
                    if (othersFragment != null && othersFragment.isAdded && othersFragment.isVisible) {
                        Log.d(TAG, "NAVIGATION_STATE_FIX: Triggering Others fragment refresh after back navigation")
                        // The Others fragment will handle its own refresh in onResume()
                    }
                }

                Log.d(TAG, "NAVIGATION_STATE_FIX: Back navigation state recovery completed successfully")
            } else {
                Log.w(TAG, "NAVIGATION_STATE_FIX: No visible fragment or current state available for recovery")
            }
        } catch (e: Exception) {
            Log.e(TAG, "NAVIGATION_STATE_FIX: Error during back navigation state recovery", e)
        }

        Log.d(TAG, "NAVIGATION_STATE_FIX: === BACK NAVIGATION STATE RECOVERY END ===")
    }

    /**
     * NAVIGATION_STATE_FIX: Detects the actually visible fragment by examining the fragment container.
     * This helps identify state desynchronization issues.
     */
    private fun detectActualVisibleFragment(): Fragment? {
        return try {
            val fragmentManager = this.fragmentManager ?: return null
            val fragments = fragmentManager.fragments
            Log.d(TAG, "NAVIGATION_STATE_FIX: Scanning ${fragments.size} fragments for visibility")

            fragments.forEach { fragment ->
                Log.d(TAG, "NAVIGATION_STATE_FIX: Fragment ${fragment.javaClass.simpleName}: visible=${fragment.isVisible}, added=${fragment.isAdded}, hidden=${fragment.isHidden}")
            }

            val visibleFragment = fragments.find { it.isVisible && it.isAdded && !it.isHidden }
            Log.d(TAG, "NAVIGATION_STATE_FIX: Detected visible fragment: ${visibleFragment?.javaClass?.simpleName}")

            visibleFragment
        } catch (e: Exception) {
            Log.e(TAG, "NAVIGATION_STATE_FIX: Error detecting visible fragment", e)
            null
        }
    }

    /**
     * NAVIGATION_STATE_FIX: Performs state synchronization check before navigation.
     * This prevents navigation issues caused by state desynchronization.
     */
    private fun performStateSynchronizationCheck() {
        try {
            val actualVisibleFragment = detectActualVisibleFragment()
            val currentState = getCurrentState()

            if (actualVisibleFragment != null && currentState != null) {
                val actualFragmentId = getFragmentId(actualVisibleFragment)

                if (actualFragmentId != null && actualFragmentId != currentState.activeFragmentId) {
                    Log.w(TAG, "NAVIGATION_STATE_FIX: ⚠️ PRE-NAVIGATION STATE DESYNC DETECTED!")
                    Log.w(TAG, "NAVIGATION_STATE_FIX: State says: ${getFragmentName(currentState.activeFragmentId)}")
                    Log.w(TAG, "NAVIGATION_STATE_FIX: Actually visible: ${getFragmentName(actualFragmentId)}")

                    // NAVIGATION_STATE_FIX: Force state synchronization
                    val correctedState = currentState.copy(activeFragmentId = actualFragmentId)
                    _navigationState.value = correctedState
                    currentActiveFragment = actualVisibleFragment

                    Log.i(TAG, "NAVIGATION_STATE_FIX: ✅ Pre-navigation state synchronized to: ${getFragmentName(actualFragmentId)}")
                } else {
                    Log.v(TAG, "NAVIGATION_STATE_FIX: Pre-navigation state check passed")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "NAVIGATION_STATE_FIX: Error during pre-navigation state synchronization", e)
        }
    }

    /**
     * Gets or creates a fragment instance with caching for performance optimization.
     */
    private fun getOrCreateFragment(fragmentId: Int): Fragment {
        // Check cache first
        fragmentCache[fragmentId]?.let { cachedFragment ->
            // MULTI_NAVIGATION_FIX: Check if fragment is in removing state or detached
            if (cachedFragment.isRemoving || cachedFragment.isDetached) {
                Log.w(TAG, "FRAGMENT_CACHE: Cached fragment ${cachedFragment.javaClass.simpleName} is in invalid state (removing=${cachedFragment.isRemoving}, detached=${cachedFragment.isDetached}), creating new instance")
                // Remove from cache and create new instance
                fragmentCache.remove(fragmentId)
                val newFragment = createFragmentInstance(fragmentId)
                fragmentCache[fragmentId] = newFragment
                fragmentCreationCount++
                Log.d(TAG, "FRAGMENT_CACHE: Created new instance to replace invalid fragment")
                return newFragment
            }

            cacheHitCount++
            Log.d(TAG, "FRAGMENT_CACHE: Cache hit for fragment ID: $fragmentId")
            return cachedFragment
        }

        // Create new fragment if not in cache
        val fragment = createFragmentInstance(fragmentId)
        fragmentCache[fragmentId] = fragment
        fragmentCreationCount++

        Log.d(TAG, "FRAGMENT_CACHE: Created new fragment for ID: $fragmentId (total cached: ${fragmentCache.size})")
        return fragment
    }

    /**
     * Creates a new fragment instance based on fragment ID.
     */
    private fun createFragmentInstance(fragmentId: Int): Fragment {
        return when (fragmentId) {
            R.id.chargeFragment -> com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment()
            R.id.dischargeFragment -> com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment()
            R.id.healthFragment -> com.tqhit.battery.one.fragment.main.HealthFragment()
            R.id.settingsFragment -> com.tqhit.battery.one.fragment.main.SettingsFragment()
            R.id.animationGridFragment -> com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment()
            R.id.othersFragment -> com.tqhit.battery.one.fragment.main.others.OthersFragment()
            else -> com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment()
        }
    }

    /**
     * Generates a unique tag for fragment identification.
     */
    private fun getFragmentTag(fragmentId: Int): String {
        return "$FRAGMENT_TAG_PREFIX$fragmentId"
    }

    /**
     * Clears the fragment cache and removes fragments from FragmentManager.
     */
    private fun clearFragmentCache() {
        val fragmentManager = this.fragmentManager
        if (fragmentManager != null && fragmentCache.isNotEmpty()) {
            Log.d(TAG, "FRAGMENT_CACHE: Clearing fragment cache (${fragmentCache.size} fragments)")

            val transaction = fragmentManager.beginTransaction()
            fragmentCache.values.forEach { fragment ->
                if (fragment.isAdded) {
                    transaction.remove(fragment)
                }
            }
            transaction.commitAllowingStateLoss()
        }

        fragmentCache.clear()
        currentActiveFragment = null
        fragmentCreationCount = 0
        cacheHitCount = 0
        lifecycleOptimizer.clear()

        Log.d(TAG, "FRAGMENT_CACHE: Fragment cache cleared")
    }

    /**
     * Validates and fixes fragment visibility states to ensure only one fragment is visible.
     * Enhanced to specifically handle AnimationGridFragment overlay issues.
     */
    private fun validateFragmentVisibility() {
        val fragmentManager = this.fragmentManager ?: return

        // Log ALL fragments in the container to detect Navigation Component conflicts
        val allFragments = fragmentManager.fragments
        Log.d(TAG, "FRAGMENT_CONTAINER_AUDIT: Total fragments in container: ${allFragments.size}")
        allFragments.forEachIndexed { index, fragment ->
            Log.d(TAG, "FRAGMENT_CONTAINER_AUDIT: [$index] ${fragment?.javaClass?.simpleName} - visible: ${fragment?.isVisible}, hidden: ${fragment?.isHidden}, added: ${fragment?.isAdded}")
        }

        // Remove any unknown fragments that might be from Navigation Component
        val unknownFragments = allFragments.filter { fragment ->
            fragment != null && fragment.isAdded && !fragmentCache.values.contains(fragment)
        }

        if (unknownFragments.isNotEmpty()) {
            Log.w(TAG, "FRAGMENT_CONTAINER_AUDIT: Found ${unknownFragments.size} unknown fragments (likely from Navigation Component), removing...")
            val cleanupTransaction = fragmentManager.beginTransaction()
            unknownFragments.forEach { fragment ->
                Log.w(TAG, "FRAGMENT_CONTAINER_AUDIT: Removing unknown fragment: ${fragment?.javaClass?.simpleName}")
                if (fragment != null) {
                    cleanupTransaction.remove(fragment)
                }
            }
            cleanupTransaction.commitNow()
        }

        try {
            val visibleFragments = fragmentCache.values.filter { it.isVisible && it.isAdded }
            val currentActiveFragmentName = currentActiveFragment?.javaClass?.simpleName ?: "none"

            Log.d(TAG, "FRAGMENT_VISIBILITY: Validating visibility - Active: $currentActiveFragmentName, Visible count: ${visibleFragments.size}")

            // Log all fragment states for debugging
            fragmentCache.values.forEach { fragment ->
                Log.d(TAG, "FRAGMENT_VISIBILITY: ${fragment.javaClass.simpleName} - visible: ${fragment.isVisible}, hidden: ${fragment.isHidden}, added: ${fragment.isAdded}")
            }

            if (visibleFragments.size > 1) {
                Log.w(TAG, "FRAGMENT_VISIBILITY: Multiple fragments visible (${visibleFragments.size}), fixing...")

                val transaction = fragmentManager.beginTransaction()
                visibleFragments.forEach { fragment ->
                    if (fragment != currentActiveFragment) {
                        transaction.hide(fragment)
                        lifecycleOptimizer.onFragmentHidden(fragment)
                        Log.w(TAG, "FRAGMENT_VISIBILITY: Force hiding ${fragment.javaClass.simpleName} to fix overlay")
                    }
                }
                transaction.commitNow()

                Log.d(TAG, "FRAGMENT_VISIBILITY: Fixed multiple visibility issue - only ${currentActiveFragmentName} should now be visible")
            } else if (visibleFragments.size == 0 && currentActiveFragment != null) {
                Log.w(TAG, "FRAGMENT_VISIBILITY: No fragments visible but should have active fragment, fixing...")

                val transaction = fragmentManager.beginTransaction()
                currentActiveFragment?.let { activeFragment ->
                    if (activeFragment.isAdded) {
                        transaction.show(activeFragment)
                        transaction.commitNow()
                        Log.d(TAG, "FRAGMENT_VISIBILITY: Restored visibility for ${activeFragment.javaClass.simpleName}")
                    }
                }
            }

            // Final verification
            val finalVisibleCount = fragmentCache.values.count { it.isVisible && it.isAdded }
            if (finalVisibleCount != 1) {
                Log.e(TAG, "FRAGMENT_VISIBILITY: Validation failed - $finalVisibleCount fragments visible after fix attempt")
            } else {
                Log.d(TAG, "FRAGMENT_VISIBILITY: Validation successful - exactly 1 fragment visible")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error validating fragment visibility", e)
        }
    }

    /**
     * Specifically handles AnimationGridFragment overlay issues.
     * This method ensures AnimationGridFragment is properly hidden when other fragments are active.
     */
    private fun fixAnimationGridFragmentOverlay(targetFragment: Fragment) {
        val fragmentManager = this.fragmentManager ?: return

        try {
            // Find AnimationGridFragment in cache
            val animationFragment = fragmentCache.values.find {
                it.javaClass.simpleName == "AnimationGridFragment"
            }

            if (animationFragment != null && animationFragment != targetFragment) {
                if (animationFragment.isVisible && animationFragment.isAdded) {
                    Log.w(TAG, "ANIMATION_OVERLAY_FIX: AnimationGridFragment is visible when ${targetFragment.javaClass.simpleName} should be active - forcing hide")

                    val transaction = fragmentManager.beginTransaction()
                    transaction.hide(animationFragment)
                    transaction.commitNow()

                    lifecycleOptimizer.onFragmentHidden(animationFragment)

                    Log.d(TAG, "ANIMATION_OVERLAY_FIX: Successfully hid AnimationGridFragment overlay")
                } else {
                    Log.d(TAG, "ANIMATION_OVERLAY_FIX: AnimationGridFragment is properly hidden (visible: ${animationFragment.isVisible}, added: ${animationFragment.isAdded})")
                }
            } else if (animationFragment == targetFragment) {
                Log.d(TAG, "ANIMATION_OVERLAY_FIX: AnimationGridFragment is the target fragment - no overlay fix needed")
            } else {
                Log.d(TAG, "ANIMATION_OVERLAY_FIX: AnimationGridFragment not found in cache")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error fixing AnimationGridFragment overlay", e)
        }
    }

    /**
     * Gets performance statistics for debugging.
     */
    fun getPerformanceStats(): String {
        val cacheStats = "Fragment Cache Stats - Cached: ${fragmentCache.size}, Created: $fragmentCreationCount, Cache Hits: $cacheHitCount, Hit Rate: ${if (fragmentCreationCount > 0) (cacheHitCount * 100 / (fragmentCreationCount + cacheHitCount)) else 0}%"
        val lifecycleStats = lifecycleOptimizer.getLifecycleStats()
        val visibilityStats = "Fragment Visibility - Active: ${currentActiveFragment?.javaClass?.simpleName ?: "none"}, Visible: ${fragmentCache.values.count { it.isVisible }}, Hidden: ${fragmentCache.values.count { it.isHidden }}"
        return "$cacheStats\n$lifecycleStats\n$visibilityStats"
    }

    /**
     * Logs the current fragment cache state for debugging multi-navigation issues.
     */
    private fun logFragmentCacheState(context: String) {
        Log.d(TAG, "FragmentCache: === FRAGMENT CACHE STATE ($context) ===")
        Log.d(TAG, "FragmentCache: Cache size: ${fragmentCache.size}")
        Log.d(TAG, "FragmentCache: Current active fragment: ${currentActiveFragment?.javaClass?.simpleName ?: "none"}")
        Log.d(TAG, "FragmentCache: Fragment creation count: $fragmentCreationCount")
        Log.d(TAG, "FragmentCache: Cache hit count: $cacheHitCount")

        if (fragmentCache.isEmpty()) {
            Log.d(TAG, "FragmentCache: Cache is empty")
        } else {
            fragmentCache.forEach { (id, fragment) ->
                val fragmentName = getFragmentName(id)
                val instanceHash = fragment.hashCode()
                Log.d(TAG, "FragmentCache: [$fragmentName] instance=$instanceHash, added=${fragment.isAdded}, visible=${fragment.isVisible}, hidden=${fragment.isHidden}, detached=${fragment.isDetached}, removing=${fragment.isRemoving}")

                // Special logging for DischargeFragment to track ViewModel preservation
                if (fragment is com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment) {
                    Log.d(TAG, "MultiNavigation: DischargeFragment instance hash: $instanceHash")
                    try {
                        // Use reflection to get ViewModel hashCode if possible
                        val viewModelField = fragment.javaClass.getDeclaredField("viewModel")
                        viewModelField.isAccessible = true
                        val viewModel = viewModelField.get(fragment)
                        if (viewModel != null) {
                            Log.d(TAG, "MultiNavigation: DischargeFragment ViewModel hash: ${viewModel.hashCode()}")
                        } else {
                            Log.d(TAG, "MultiNavigation: DischargeFragment ViewModel is null")
                        }
                    } catch (e: Exception) {
                        Log.d(TAG, "MultiNavigation: Could not access DischargeFragment ViewModel: ${e.message}")
                    }
                }
            }
        }

        // Log fragment manager state
        val fragmentManager = this.fragmentManager
        if (fragmentManager != null) {
            Log.d(TAG, "FragmentCache: FragmentManager back stack count: ${fragmentManager.backStackEntryCount}")
            Log.d(TAG, "FragmentCache: FragmentManager fragments count: ${fragmentManager.fragments.size}")
        } else {
            Log.d(TAG, "FragmentCache: FragmentManager is null")
        }

        Log.d(TAG, "FragmentCache: === END FRAGMENT CACHE STATE ($context) ===")
    }
}
