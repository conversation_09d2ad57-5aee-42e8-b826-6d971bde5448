package com.tqhit.battery.one.fragment.main

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.View
import android.widget.ImageButton
import android.widget.Toast
import androidx.fragment.app.viewModels
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.FragmentSettingsBinding
import com.tqhit.battery.one.dialog.capacity.SetupPasswordDialog
import com.tqhit.battery.one.dialog.theme.SelectThemeDialog
import com.tqhit.battery.one.dialog.theme.SelectColorDialog
import com.tqhit.battery.one.dialog.language.SelectLanguageDialog
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import kotlin.getValue
import androidx.core.net.toUri
//import com.applovin.sdk.AppLovinSdk
//import com.applovin.sdk.AppLovinSdkConfiguration
//import com.applovin.sdk.AppLovinSdkConfiguration.ConsentFlowUserGeography
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.databinding.ItemSlideLayout6Binding
import com.tqhit.battery.one.features.navigation.AppNavigator
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel

@AndroidEntryPoint
class SettingsFragment : AdLibBaseFragment<FragmentSettingsBinding>() {
    override val binding by lazy {
        FragmentSettingsBinding.inflate(layoutInflater)
    }
    private val appViewModel: AppViewModel by viewModels()
    private val batteryViewModel: BatteryViewModel by viewModels()
    @Inject lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager
    @Inject lateinit var preferencesHelper: PreferencesHelper
    @Inject lateinit var appNavigator: AppNavigator
    private lateinit var btnBackNavigation: ImageButton

    companion object {
        private const val TAG = "SettingsFragment"
    }

    override fun setupUI() {
        super.setupUI()
        setupBackNavigation()
        setupWorkInBackgroundButton()
        setupPrivacySettingButton()
        setupPrivacyButton()
        setupThemeChangeButton()
        setupColorChangeButton()
        setupLanguageChangeButton()
        setupVibrationSwitch()
        setupChargeNotificationSwitch()
        setupDischargeNotificationSwitch()
        setupNotificationSettingsButton()
        setupAnimationOverlaySwitch()
        setupAnimationOverlayTimeSwitch()
        setupAntiThief()
        setupAntiThiefSwitches()
        setupDebuggerButton()
    }

    /**
     * Sets up back navigation button click handling.
     * Uses proper Navigation Component back stack management.
     */
    private fun setupBackNavigation() {
        Log.d(TAG, "BACK_NAVIGATION: Setting up back navigation button")

        btnBackNavigation = binding.includeBackNavigation.btnBackNavigation

        btnBackNavigation.setOnClickListener {
            Log.d(TAG, "BACK_NAVIGATION: Back button clicked - using centralized AppNavigator")
            Log.d(TAG, "MultiNavigation: SettingsFragment back navigation initiated via AppNavigator")
            Log.d(TAG, "FragmentCache: Using AppNavigator to preserve fragment cache")

            try {
                // Initialize AppNavigator if needed
                initializeAppNavigatorIfNeeded()

                // Try AppNavigator first (modern centralized approach)
                val navigationSuccess = appNavigator.navigateToOthers()

                if (navigationSuccess) {
                    Log.d(TAG, "BACK_NAVIGATION: Successfully navigated back using AppNavigator")
                    Log.d(TAG, "MultiNavigation: AppNavigator preserved fragment lifecycle")
                } else {
                    Log.w(TAG, "BACK_NAVIGATION: AppNavigator failed - using legacy fallback")
                    navigateBackLegacy()
                }
            } catch (e: Exception) {
                Log.e(TAG, "BACK_NAVIGATION: Error during AppNavigator navigation", e)
                Log.e(TAG, "MultiNavigation: AppNavigator failed - attempting legacy fallback")
                navigateBackLegacy()
            }
        }
    }

    /**
     * Initializes AppNavigator if not already initialized.
     * This ensures centralized navigation is available for back navigation.
     */
    private fun initializeAppNavigatorIfNeeded() {
        if (!appNavigator.isInitialized()) {
            Log.d(TAG, "APPNAVIGATOR_INIT: Initializing AppNavigator from SettingsFragment")
            try {
                val activity = requireActivity()
                val fragmentManager = activity.supportFragmentManager
                val bottomNavigationView = activity.findViewById<com.google.android.material.bottomnavigation.BottomNavigationView>(R.id.bottom_view)
                val fragmentContainerId = R.id.nav_host_fragment

                if (bottomNavigationView != null) {
                    appNavigator.initialize(
                        fragmentManager = fragmentManager,
                        bottomNavigationView = bottomNavigationView,
                        fragmentContainerId = fragmentContainerId,
                        lifecycleOwner = this
                    )
                    Log.d(TAG, "APPNAVIGATOR_INIT: AppNavigator initialized successfully")
                } else {
                    Log.e(TAG, "APPNAVIGATOR_INIT: BottomNavigationView not found")
                }
            } catch (e: Exception) {
                Log.e(TAG, "APPNAVIGATOR_INIT: Error initializing AppNavigator", e)
            }
        }
    }

    /**
     * Legacy back navigation using MainActivity's navigation system.
     * Used as fallback when AppNavigator fails.
     */
    private fun navigateBackLegacy() {
        Log.d(TAG, "BACK_NAVIGATION: Using legacy MainActivity navigation")

        try {
            // MULTI_NAVIGATION_FIX: Use MainActivity's navigation system instead of direct popBackStack
            // This preserves the DynamicNavigationManager's fragment cache and show/hide pattern
            val mainActivity = requireActivity() as? com.tqhit.battery.one.activity.main.MainActivity
            if (mainActivity != null) {
                Log.d(TAG, "MultiNavigation: Using MainActivity's navigation system to preserve fragment cache")
                mainActivity.navigateToOthersFragment()
                Log.d(TAG, "BACK_NAVIGATION: Successfully navigated back using MainActivity navigation")
            } else {
                Log.w(TAG, "MultiNavigation: MainActivity not available - using popBackStack fallback")
                Log.w(TAG, "FragmentCache: WARNING - popBackStack may corrupt fragment cache")

                // Use the fragment manager to navigate back
                // This will properly handle the back stack and return to Others fragment
                requireActivity().supportFragmentManager.popBackStack()
                Log.d(TAG, "BACK_NAVIGATION: Successfully navigated back using popBackStack()")
            }
        } catch (e: Exception) {
            Log.e(TAG, "BACK_NAVIGATION: Error in legacy navigation", e)
            Log.e(TAG, "MultiNavigation: Legacy navigation failed - attempting emergency fallback")

            // Emergency fallback: Try popBackStack only (avoid replace() to preserve fragment cache)
            try {
                val fragmentManager = requireActivity().supportFragmentManager
                if (fragmentManager.backStackEntryCount > 0) {
                    fragmentManager.popBackStack()
                    fragmentManager.executePendingTransactions()
                    Log.d(TAG, "BACK_NAVIGATION: Emergency fallback using popBackStack() successful")
                } else {
                    Log.e(TAG, "BACK_NAVIGATION: No back stack entries - cannot navigate back safely")
                    Log.e(TAG, "FragmentCache: Cannot preserve fragment cache without proper navigation")
                }
            } catch (fallbackException: Exception) {
                Log.e(TAG, "BACK_NAVIGATION: All navigation attempts failed", fallbackException)
                Log.e(TAG, "MultiNavigation: CRITICAL - All navigation methods failed")
            }
        }
    }

    private fun setupWorkInBackgroundButton() {
        binding.workInBackgoundButton.setOnClickListener {
            requestBatteryOptimizationPermission()
        }
    }

    private fun setupPrivacySettingButton() {
        binding.privacySettingButton.visibility = appViewModel.isConsentFlowUserGeography()
            .let { if (it) View.VISIBLE else View.GONE }
        binding.privacySettingButton.setOnClickListener {
//            val cmpService = AppLovinSdk.getInstance(requireActivity()).cmpService
//            cmpService.showCmpForExistingUser(requireActivity()) { error ->
//                if (null == error)
//                {
//                    // The CMP alert was shown successfully.
//                }
//            }
        }
    }

    private fun setupPrivacyButton() {
        binding.privacyButton.setOnClickListener {
            startActivity(Intent(Intent.ACTION_VIEW, appViewModel.getPrivacyPolicyUrl().toUri()))
        }
    }
    
    private fun setupThemeChangeButton() {
        binding.changeTheme.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showThemeDialog()
            }
        }
    }

    private fun setupColorChangeButton() {
        binding.changeSecondColorTheme.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showColorDialog()
            }
        }
    }

    private fun setupLanguageChangeButton() {
        binding.changeLang.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showLanguageDialog()
            }
        }
    }
    
    private fun setupVibrationSwitch() {
        binding.switchVibration.isChecked = appViewModel.isVibrationEnabled()
        binding.switchVibration.setOnCheckedChangeListener { _, isChecked ->
            appViewModel.setVibrationEnabled(isChecked)
        }
    }

    private fun setupChargeNotificationSwitch() {
        binding.switchIsChargeNotify.isChecked = appViewModel.isChargeNotificationEnabled()
        binding.switchIsChargeNotify.setOnCheckedChangeListener { _, isChecked ->
            appViewModel.setChargeNotificationEnabled(isChecked)
        }
    }

    private fun setupDischargeNotificationSwitch() {
        binding.switchIsDischargeNotify.isChecked = appViewModel.isDischargeNotificationEnabled()
        binding.switchIsDischargeNotify.setOnCheckedChangeListener { _, isChecked ->
            appViewModel.setDischargeNotificationEnabled(isChecked)
        }
    }
    
    private fun setupNotificationSettingsButton() {
        binding.buttonSettingsNotify.setOnClickListener {
            val intent = Intent().apply {
                action = android.provider.Settings.ACTION_APP_NOTIFICATION_SETTINGS
                putExtra(android.provider.Settings.EXTRA_APP_PACKAGE, requireContext().packageName)
            }
            startActivity(intent)
        }
    }

    private fun setupAnimationOverlaySwitch() {
        binding.switchEnableAnimation.isChecked = appViewModel.isAnimationOverlayEnabled()
        binding.switchAnimationTimeBlock.visibility = if (binding.switchEnableAnimation.isChecked) {
            View.VISIBLE
        } else {
            View.GONE
        }
        binding.switchEnableAnimation.setOnCheckedChangeListener { _, isChecked ->
            appViewModel.setAnimationOverlayEnabled(isChecked)
            binding.switchAnimationTimeBlock.visibility = if (isChecked) {
                View.VISIBLE
            } else {
                View.GONE
            }
        }
    }

    private fun setupAntiThief() {
        binding.antiThiefInfo.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                NotificationDialog(
                    context = requireContext(),
                    title = getString(R.string.anti_thief),
                    message = getString(R.string.anti_thief_info),
                ).show()
            }
        }
    }

    private fun setupAnimationOverlayTimeSwitch() {
        binding.switchEnableAnimationTime.isChecked = appViewModel.isAnimationOverlayTimeEnabled()
        binding.switchEnableAnimationTime.setOnCheckedChangeListener { _, isChecked ->
            appViewModel.setAnimationOverlayTimeEnabled(isChecked)
        }
    }
    
    private fun showThemeDialog() {
        if (!isAdded) return
        SelectThemeDialog(
            activity = requireActivity()
        ).show()
    }

    private fun showColorDialog() {
        if (!isAdded) return
        SelectColorDialog(
            activity = requireActivity()
        ).show()
    }

    private fun showLanguageDialog() {
        if (!isAdded) return
        SelectLanguageDialog(
            activity = requireActivity(),
            appViewModel
        ).show()
    }

    private fun setupAntiThiefSwitches() {
        // Anti-Thief Enable Switch
        binding.switchEnableAntiThief.isChecked = appViewModel.isAntiThiefEnabled()
        binding.switchEnableAntiThief.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                if (!appViewModel.isAntiThiefPasswordSet()) {
                    SetupPasswordDialog(
                        context = requireContext(),
                        onConfirm = { password ->
                            if (password.isNotBlank()) {
                                appViewModel.setAntiThiefPassword(password)
                                appViewModel.setAntiThiefEnabled(true)
                                binding.switchEnableAntiThief.isChecked = true
                            } else {
                                binding.switchEnableAntiThief.isChecked = false
                            }
                        },
                        onCancel = {
                            binding.switchEnableAntiThief.isChecked = false
                        }
                    ).show()
                } else {
                    appViewModel.setAntiThiefEnabled(true)
                }
            } else {
                appViewModel.setAntiThiefPassword("")
                appViewModel.setAntiThiefEnabled(false)
            }
        }

        // Anti-Thief Sound Switch
        binding.switchEnableAntiThiefSound.isChecked = appViewModel.isAntiThiefSoundEnabled()
        binding.switchEnableAntiThiefSound.setOnCheckedChangeListener { _, isChecked ->
            appViewModel.setAntiThiefSoundEnabled(isChecked)
        }
    }

    private fun setupDebuggerButton() {
        android.util.Log.d("SettingsFragment", "Setting up debug button")

        // Make sure the debug section is visible
        binding.testNewDischarge.visibility = View.VISIBLE

        binding.testNewDischarge.setOnClickListener {
            android.util.Log.d("SettingsFragment", "Debug button clicked")
            // Legacy TestNewDischargeActivity removed - opening main activity instead
            val intent = Intent(requireContext(), com.tqhit.battery.one.activity.main.MainActivity::class.java)
            startActivity(intent)
        }

        // Log the current visibility state
        android.util.Log.d("SettingsFragment", "Debug button visibility: ${binding.testNewDischarge.visibility == View.VISIBLE}")

        binding.debuggerButton.setOnClickListener {
//            AppLovinSdk.getInstance( context ).showCreativeDebugger()
        }
        binding.mediationDebuggerButton.setOnClickListener {
//            AppLovinSdk.getInstance( context ).showMediationDebugger()
        }
    }

    private fun requestBatteryOptimizationPermission() {
        try {
            if (batteryViewModel.isIgnoringBatteryOptimizations()) {
                Toast.makeText(requireContext(), R.string.permission_granted, Toast.LENGTH_SHORT).show()
                return
            }

            val intent =
                Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                    data = "package:${requireContext().packageName}".toUri()
                }
            startActivity(intent)
        } catch (e: Exception) {
            Toast.makeText(requireContext(), R.string.unexpected_error, Toast.LENGTH_SHORT).show()
        }
    }
}