package com.tqhit.battery.one.fragment.main.others

import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.fragment.app.viewModels
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.databinding.FragmentOthersBinding
import com.tqhit.battery.one.dialog.capacity.SetupPasswordDialog
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.features.navigation.DynamicNavigationManager
import com.tqhit.battery.one.features.navigation.AppNavigator
import com.tqhit.battery.one.features.navigation.SharedNavigationViewModel
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.fragment.main.HealthFragment
import com.tqhit.battery.one.fragment.main.SettingsFragment
import com.tqhit.battery.one.fragment.main.others.adapter.OthersAdapter
import com.tqhit.battery.one.fragment.main.others.data.OthersItemData
import com.tqhit.battery.one.fragment.main.others.data.OthersItemData.Companion.CHARGE_DISCHARGE_ITEM_ID
import com.tqhit.battery.one.fragment.main.others.data.OthersItemData.Companion.HEALTH_ITEM_ID
import com.tqhit.battery.one.fragment.main.others.data.OthersItemData.Companion.SETTINGS_ITEM_ID
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Others fragment implementing card-based navigation for Charge/Discharge, Battery Health, and Settings.
 *
 * Features:
 * - Dynamic charge/discharge navigation based on real-time battery status
 * - CoreBatteryStatsService integration for battery state monitoring
 * - Anti-theft toggle functionality with password management
 * - MVI architecture pattern with proper state management
 * - Comprehensive logging for debugging and ADB testing
 *
 * Following the established stats module architecture pattern.
 */
@AndroidEntryPoint
class OthersFragment : AdLibBaseFragment<FragmentOthersBinding>() {

    override val binding by lazy { FragmentOthersBinding.inflate(layoutInflater) }

    // Modern architecture dependencies
    @Inject lateinit var coreBatteryStatsProvider: CoreBatteryStatsProvider
    @Inject lateinit var dynamicNavigationManager: DynamicNavigationManager
    @Inject lateinit var appNavigator: AppNavigator
    @Inject lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager

    private val appViewModel: AppViewModel by viewModels()
    private val sharedNavigationViewModel: SharedNavigationViewModel by activityViewModels()
    private lateinit var othersAdapter: OthersAdapter
    private var isDeviceCharging: Boolean = false

    // CLICK_LISTENER_FIX: Click debouncing variables
    private var lastClickTime: Long = 0L

    // NAVIGATION_INVESTIGATION: Click counter and state tracking
    private var totalClickAttempts: Int = 0
    private var successfulNavigations: Int = 0
    private var failedNavigations: Int = 0
    private var lastNavigationSource: String = "UNKNOWN"
    private var fragmentCreationTime: Long = System.currentTimeMillis()
    private var lastBackNavigationTime: Long = 0L

    // BATTERY_OBSERVATION_FIX: Track battery observation state to prevent multiple concurrent observations
    private var isBatteryObservationActive: Boolean = false
    private var lastBatteryStateUpdateTime: Long = 0L

    // BUTTON_FLICKERING_FIX: Debounce UI updates to prevent rapid button state changes
    private var pendingUIUpdateJob: kotlinx.coroutines.Job? = null

    companion object {
        private const val TAG = "OthersFragment"
        private const val CLICK_DEBOUNCE_DELAY = 500L // 500ms debounce delay
    }

    init {
        Log.d(TAG, "OTHERS_FRAGMENT: Fragment instance created")
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.d(TAG, "NAVIGATION_INVESTIGATION: === FRAGMENT VIEW CREATED ===")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Fragment instance created at: $fragmentCreationTime")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Resetting click counters for new view")

        // Reset counters for new view creation
        totalClickAttempts = 0
        successfulNavigations = 0
        failedNavigations = 0
        lastNavigationSource = "VIEW_CREATED"

        Log.d(TAG, "OTHERS_FRAGMENT: onViewCreated - initializing Others fragment")
        Log.d(TAG, "OTHERS_LIFECYCLE: Fragment view created, starting battery status observation")

        // Initialize AppNavigator for centralized navigation
        initializeAppNavigator()

        // Initialize SharedNavigationViewModel observation
        observeNavigationState()

        observeBatteryStatus()
    }

    override fun onResume() {
        super.onResume()
        val currentTime = System.currentTimeMillis()
        lastBackNavigationTime = currentTime

        Log.d(TAG, "NAVIGATION_INVESTIGATION: === FRAGMENT RESUME ===")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Fragment resumed at ${currentTime}")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Time since creation: ${currentTime - fragmentCreationTime}ms")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Last navigation source: $lastNavigationSource")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Click statistics - Total: $totalClickAttempts, Success: $successfulNavigations, Failed: $failedNavigations")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Fragment state - isAdded: $isAdded, isVisible: $isVisible, isDetached: $isDetached")

        // Detect if we're resuming from a back navigation
        if (totalClickAttempts > 0) {
            Log.d(TAG, "NAVIGATION_INVESTIGATION: 🔄 RESUMING AFTER NAVIGATION - This could be from back navigation")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: Previous navigation success rate: ${if (totalClickAttempts > 0) (successfulNavigations * 100 / totalClickAttempts) else 0}%")
        }

        Log.d(TAG, "OTHERS_LIFECYCLE: Fragment resumed, refreshing UI state")

        // Refresh anti-theft toggle state in case it was changed in settings
        try {
            binding.switchEnableAntiThief.isChecked = appViewModel.isAntiThiefEnabled()
            Log.d(TAG, "OTHERS_ANTITHEFT: Refreshed toggle state on resume - ${binding.switchEnableAntiThief.isChecked}")
        } catch (e: Exception) {
            Log.w(TAG, "OTHERS_LIFECYCLE: Binding not available during resume", e)
        }

        // BACK_NAVIGATION_BLANK_FIX: Check if UI refresh is needed on resume
        // This is crucial for fixing the blank Others Fragment issue after back navigation
        Log.d(TAG, "BACK_NAVIGATION_BLANK_FIX: Checking if UI refresh is needed on resume")

        // Check if adapter is initialized and has items
        if (::othersAdapter.isInitialized) {
            val currentItemCount = othersAdapter.itemCount
            Log.d(TAG, "BACK_NAVIGATION_BLANK_FIX: Current adapter item count: $currentItemCount")

            if (currentItemCount == 0) {
                Log.w(TAG, "BACK_NAVIGATION_BLANK_FIX: Adapter is empty, forcing UI refresh")
                refreshAdapterAndListeners()
            } else {
                Log.d(TAG, "BACK_NAVIGATION_BLANK_FIX: Adapter has items, will refresh anyway for consistency")
                // CLICK_LISTENER_FIX: Ensure adapter and click listeners are properly restored
                refreshAdapterAndListeners()
            }
        } else {
            Log.w(TAG, "BACK_NAVIGATION_BLANK_FIX: Adapter not initialized, forcing full refresh")
            refreshAdapterAndListeners()
        }

        // BACK_NAVIGATION_BLANK_FIX: Ensure battery observation is active
        // This is crucial because battery observation might have been stopped when fragment was hidden
        if (!isBatteryObservationActive) {
            Log.w(TAG, "BACK_NAVIGATION_BLANK_FIX: Battery observation not active, restarting")
            observeBatteryStatus()
        } else {
            Log.d(TAG, "BACK_NAVIGATION_BLANK_FIX: Battery observation is active")
        }

        // NAVIGATION_STATE_FIX: Trigger state recovery if this is a back navigation
        if (totalClickAttempts > 0) {
            Log.d(TAG, "NAVIGATION_STATE_FIX: Triggering state recovery after back navigation")
            try {
                dynamicNavigationManager.handleBackNavigationStateRecovery()
            } catch (e: Exception) {
                Log.e(TAG, "NAVIGATION_STATE_FIX: Error during state recovery", e)
            }
        }

        Log.d(TAG, "NAVIGATION_INVESTIGATION: === FRAGMENT RESUME COMPLETED ===")
    }



    override fun onPause() {
        super.onPause()
        Log.d(TAG, "OTHERS_LIFECYCLE: Fragment paused")

        // BUTTON_FLICKERING_FIX: Cancel any pending UI updates when fragment is paused
        pendingUIUpdateJob?.cancel()
        Log.d(TAG, "BUTTON_FLICKERING_FIX: Cancelled pending UI updates on pause")
    }

    override fun onDestroyView() {
        Log.d(TAG, "OTHERS_LIFECYCLE: Fragment view being destroyed")

        // BATTERY_OBSERVATION_FIX: Reset observation state when view is destroyed
        isBatteryObservationActive = false
        pendingUIUpdateJob?.cancel()
        Log.d(TAG, "BATTERY_OBSERVATION_FIX: Reset battery observation state on view destroy")

        super.onDestroyView()
    }

    override fun setupData() {
        super.setupData()
        Log.d(TAG, "OTHERS_FRAGMENT: setupData - configuring RecyclerView and adapter")

        setupRecyclerView()
        updateAdapterItems()
    }

    override fun setupListener() {
        super.setupListener()
        Log.d(TAG, "OTHERS_FRAGMENT: setupListener - configuring anti-theft toggle")

        setupAntiTheftToggle()
    }

    /**
     * Sets up the RecyclerView with the OthersAdapter.
     * Configures linear layout manager and item click handling.
     */
    private fun setupRecyclerView() {
        Log.d(TAG, "OTHERS_RECYCLERVIEW: Setting up RecyclerView with LinearLayoutManager")

        // CLICK_LISTENER_FIX: Only create adapter if not already created
        if (!::othersAdapter.isInitialized) {
            othersAdapter = OthersAdapter { item ->
                handleItemClick(item)
            }
            Log.d(TAG, "OTHERS_RECYCLERVIEW: Created new adapter instance")
        } else {
            Log.d(TAG, "OTHERS_RECYCLERVIEW: Reusing existing adapter instance")
        }

        binding.othersRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = othersAdapter
        }

        Log.d(TAG, "OTHERS_RECYCLERVIEW: RecyclerView setup complete")
    }

    /**
     * CLICK_LISTENER_FIX: Refreshes adapter and ensures click listeners are working.
     * Called on fragment resume to restore functionality after back navigation.
     */
    private fun refreshAdapterAndListeners() {
        Log.d(TAG, "OTHERS_CLICK_FIX: Refreshing adapter and click listeners")

        try {
            // Ensure adapter is properly attached
            if (::othersAdapter.isInitialized && binding.othersRecyclerView.adapter == null) {
                Log.w(TAG, "OTHERS_CLICK_FIX: Adapter was detached, reattaching")
                binding.othersRecyclerView.adapter = othersAdapter
            }

            // Force adapter to refresh its items and click listeners
            if (::othersAdapter.isInitialized) {
                val currentItems = othersAdapter.currentList
                Log.d(TAG, "OTHERS_CLICK_FIX: Refreshing ${currentItems.size} adapter items")
                othersAdapter.notifyDataSetChanged()
            }

            // Update adapter items to ensure they reflect current battery state
            updateAdapterItems()

            Log.d(TAG, "OTHERS_CLICK_FIX: Adapter and click listeners refreshed successfully")
        } catch (e: Exception) {
            Log.e(TAG, "OTHERS_CLICK_FIX: Error refreshing adapter and listeners", e)
            // Fallback: recreate the adapter
            try {
                setupRecyclerView()
                updateAdapterItems()
                Log.d(TAG, "OTHERS_CLICK_FIX: Fallback adapter recreation completed")
            } catch (fallbackException: Exception) {
                Log.e(TAG, "OTHERS_CLICK_FIX: Fallback adapter recreation also failed", fallbackException)
            }
        }
    }

    /**
     * Updates the adapter items based on current battery charging state.
     * Creates appropriate card data for Charge/Discharge, Health, and Settings.
     * ENHANCED_DEBUG_LOGGING: Comprehensive UI update tracking.
     */
    private fun updateAdapterItems() {
        val updateStartTime = System.currentTimeMillis()

        // ENHANCED_DEBUG_LOGGING: UI update tracking
        Log.d(TAG, "UI_UPDATE: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "UI_UPDATE: 🎨 ADAPTER ITEMS UPDATE")
        Log.d(TAG, "UI_UPDATE: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "UI_UPDATE: Current charging state: $isDeviceCharging")
        Log.d(TAG, "UI_UPDATE: Fragment lifecycle state: isAdded=$isAdded, isVisible=$isVisible")
        Log.d(TAG, "UI_UPDATE: Adapter initialized: ${::othersAdapter.isInitialized}")
        Log.d(TAG, "UI_UPDATE: Update timestamp: $updateStartTime")

        val items = listOf(
            createChargeDischargeItem(),
            createHealthItem(),
            createSettingsItem()
        )

        // ENHANCED_DEBUG_LOGGING: Item creation details
        Log.d(TAG, "UI_UPDATE: Created ${items.size} adapter items:")
        items.forEachIndexed { index, item ->
            Log.d(TAG, "UI_UPDATE:   [$index] ${item.title} (ID: ${item.id})")
        }

        othersAdapter.submitList(items)
        val updateDuration = System.currentTimeMillis() - updateStartTime
        Log.d(TAG, "UI_UPDATE: ✅ Adapter update completed in ${updateDuration}ms")
        Log.d(TAG, "UI_UPDATE: ═══════════════════════════════════════════════════════════")
    }

    /**
     * Creates the charge/discharge item based on current battery status.
     * Dynamically updates title, description, and icon based on charging state.
     * ENHANCED_DEBUG_LOGGING: Button state creation tracking.
     */
    private fun createChargeDischargeItem(): OthersItemData {
        val creationTime = System.currentTimeMillis()

        // ENHANCED_DEBUG_LOGGING: Button state creation tracking
        Log.d(TAG, "BUTTON_STATE: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "BUTTON_STATE: 🔘 CHARGE/DISCHARGE BUTTON CREATION")
        Log.d(TAG, "BUTTON_STATE: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "BUTTON_STATE: Battery charging state: $isDeviceCharging")
        Log.d(TAG, "BUTTON_STATE: Creation timestamp: $creationTime")
        Log.d(TAG, "BUTTON_STATE: Time since last update: ${creationTime - lastBatteryStateUpdateTime}ms")

        return if (isDeviceCharging) {
            Log.d(TAG, "BUTTON_STATE: 🔌 Creating CHARGE button (device is charging)")
            Log.d(TAG, "BUTTON_STATE: Button will navigate to: StatsChargeFragment")
            OthersItemData(
                id = CHARGE_DISCHARGE_ITEM_ID,
                title = getString(R.string.charge),
                description = "View charging statistics and power consumption details",
                iconResId = R.drawable.ic_charge_icon
            ).also {
                Log.d(TAG, "BUTTON_STATE: ✅ CHARGE button created successfully")
            }
        } else {
            Log.d(TAG, "BUTTON_STATE: 🔋 Creating DISCHARGE button (device not charging)")
            Log.d(TAG, "BUTTON_STATE: Button will navigate to: DischargeFragment")
            OthersItemData(
                id = CHARGE_DISCHARGE_ITEM_ID,
                title = getString(R.string.discharge),
                description = "View discharge statistics and power consumption details",
                iconResId = R.drawable.ic_discharge_icon
            ).also {
                Log.d(TAG, "BUTTON_STATE: ✅ DISCHARGE button created successfully")
            }
        }.also {
            Log.d(TAG, "BUTTON_STATE: ═══════════════════════════════════════════════════════════")
        }
    }

    /**
     * Creates the health item for battery health navigation.
     */
    private fun createHealthItem(): OthersItemData {
        Log.d(TAG, "OTHERS_ITEMS: Creating health item")
        return OthersItemData(
            id = HEALTH_ITEM_ID,
            title = getString(R.string.health),
            description = "View battery health stats including capacity, voltage, and temperature",
            iconResId = R.drawable.ic_health_icon
        )
    }

    /**
     * Creates the settings item for app settings navigation.
     */
    private fun createSettingsItem(): OthersItemData {
        Log.d(TAG, "OTHERS_ITEMS: Creating settings item")
        return OthersItemData(
            id = SETTINGS_ITEM_ID,
            title = getString(R.string.settings),
            description = "Customize app preferences and behavior to fit your needs",
            iconResId = R.drawable.ic_settings_icon
        )
    }

    /**
     * Handles click events for card items.
     * Routes to appropriate fragments based on item ID and current battery state.
     */
    private fun handleItemClick(item: OthersItemData) {
        totalClickAttempts++
        val currentTime = System.currentTimeMillis()
        val timeSinceCreation = currentTime - fragmentCreationTime
        val timeSinceLastBackNav = currentTime - lastBackNavigationTime

        Log.d(TAG, "NAVIGATION_INVESTIGATION: === CLICK ATTEMPT #$totalClickAttempts ===")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Item clicked: ${item.id}")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Fragment state - isAdded: $isAdded, isVisible: $isVisible, isDetached: $isDetached, isRemoving: $isRemoving")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Activity state - activity: ${activity != null}, context: ${context != null}")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Fragment lifecycle - created ${timeSinceCreation}ms ago, last back nav ${timeSinceLastBackNav}ms ago")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Last navigation source: $lastNavigationSource")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Adapter state - initialized: ${::othersAdapter.isInitialized}")
        if (::othersAdapter.isInitialized) {
            Log.d(TAG, "NAVIGATION_INVESTIGATION: Adapter items count: ${othersAdapter.currentList.size}")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: RecyclerView adapter attached: ${binding.othersRecyclerView.adapter != null}")
        }
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Battery state - charging: $isDeviceCharging")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Success rate: $successfulNavigations/$totalClickAttempts (${if (totalClickAttempts > 0) (successfulNavigations * 100 / totalClickAttempts) else 0}%)")

        // CLICK_LISTENER_FIX: Add defensive checks to ensure fragment is in proper state
        if (!isAdded || isDetached || activity == null) {
            failedNavigations++
            Log.w(TAG, "NAVIGATION_INVESTIGATION: ❌ NAVIGATION FAILED - Fragment not in proper state")
            Log.w(TAG, "NAVIGATION_INVESTIGATION: Failed navigation #$failedNavigations - isAdded: $isAdded, isDetached: $isDetached, activity: ${activity != null}")
            return
        }

        // CLICK_LISTENER_FIX: Add debouncing to prevent rapid clicks
        if (currentTime - lastClickTime < CLICK_DEBOUNCE_DELAY) {
            Log.d(TAG, "NAVIGATION_INVESTIGATION: Click ignored due to debouncing (${currentTime - lastClickTime}ms < ${CLICK_DEBOUNCE_DELAY}ms)")
            return
        }
        lastClickTime = currentTime

        when (item.id) {
            CHARGE_DISCHARGE_ITEM_ID -> {
                Log.d(TAG, "NAVIGATION_INVESTIGATION: Processing charge/discharge navigation using AppNavigator...")
                val navigationResult = handleChargeDischargeNavigationWithAppNavigator()
                if (navigationResult) {
                    successfulNavigations++
                    Log.d(TAG, "NAVIGATION_INVESTIGATION: ✅ NAVIGATION SUCCESSFUL #$successfulNavigations")
                } else {
                    failedNavigations++
                    Log.e(TAG, "NAVIGATION_INVESTIGATION: ❌ NAVIGATION FAILED #$failedNavigations")
                }
            }
            HEALTH_ITEM_ID -> {
                Log.d(TAG, "OTHERS_NAVIGATION: Navigating to HealthFragment using AppNavigator")
                val navigationResult = appNavigator.navigateToHealth()
                if (navigationResult) {
                    successfulNavigations++
                    Log.d(TAG, "OTHERS_NAVIGATION: ✅ Health navigation successful")
                } else {
                    failedNavigations++
                    Log.e(TAG, "OTHERS_NAVIGATION: ❌ Health navigation failed")
                }
            }
            SETTINGS_ITEM_ID -> {
                Log.d(TAG, "OTHERS_NAVIGATION: Navigating to SettingsFragment using AppNavigator")
                val navigationResult = appNavigator.navigateToSettings()
                if (navigationResult) {
                    successfulNavigations++
                    Log.d(TAG, "OTHERS_NAVIGATION: ✅ Settings navigation successful")
                } else {
                    failedNavigations++
                    Log.e(TAG, "OTHERS_NAVIGATION: ❌ Settings navigation failed")
                }
            }
            else -> {
                failedNavigations++
                Log.w(TAG, "NAVIGATION_INVESTIGATION: ❌ Unknown item clicked - ${item.id}")
            }
        }

        Log.d(TAG, "NAVIGATION_INVESTIGATION: === CLICK ATTEMPT #$totalClickAttempts COMPLETED ===")
    }

    /**
     * Handles charge/discharge navigation using the centralized AppNavigator.
     * Falls back to legacy DynamicNavigationManager if AppNavigator fails.
     * @return true if navigation was successful, false otherwise
     */
    private fun handleChargeDischargeNavigationWithAppNavigator(): Boolean {
        Log.d(TAG, "NAVIGATION_INVESTIGATION: === APPNAVIGATOR CHARGE/DISCHARGE NAVIGATION START ===")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Current battery charging state: $isDeviceCharging")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: AppNavigator initialized: ${appNavigator.isInitialized()}")

        // Try AppNavigator first (modern approach)
        val navigationResult = if (isDeviceCharging) {
            Log.d(TAG, "NAVIGATION_INVESTIGATION: Device charging - using AppNavigator.navigateToCharge()")
            appNavigator.navigateToCharge()
        } else {
            Log.d(TAG, "NAVIGATION_INVESTIGATION: Device not charging - using AppNavigator.navigateToDischarge()")
            appNavigator.navigateToDischarge()
        }

        if (navigationResult) {
            Log.d(TAG, "NAVIGATION_INVESTIGATION: ✅ AppNavigator navigation successful")
            return true
        } else {
            Log.w(TAG, "NAVIGATION_INVESTIGATION: ❌ AppNavigator failed, falling back to legacy navigation")
            return handleChargeDischargeNavigationLegacy()
        }
    }

    /**
     * Legacy charge/discharge navigation using DynamicNavigationManager.
     * Kept as fallback for compatibility.
     * @return true if navigation was successful, false otherwise
     */
    private fun handleChargeDischargeNavigationLegacy(): Boolean {
        Log.d(TAG, "NAVIGATION_INVESTIGATION: === LEGACY CHARGE/DISCHARGE NAVIGATION START ===")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Current battery charging state: $isDeviceCharging")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: DynamicNavigationManager initialized: ${dynamicNavigationManager.isInitialized()}")

        var navigationSuccess = false

        // Try DynamicNavigationManager first
        if (dynamicNavigationManager.isInitialized()) {
            val targetFragmentId = if (isDeviceCharging) {
                Log.d(TAG, "NAVIGATION_INVESTIGATION: Device charging - targeting charge fragment (R.id.chargeFragment)")
                R.id.chargeFragment
            } else {
                Log.d(TAG, "NAVIGATION_INVESTIGATION: Device not charging - targeting discharge fragment (R.id.dischargeFragment)")
                R.id.dischargeFragment
            }

            Log.d(TAG, "NAVIGATION_INVESTIGATION: Calling dynamicNavigationManager.handleUserNavigation($targetFragmentId)")
            navigationSuccess = dynamicNavigationManager.handleUserNavigation(targetFragmentId)
            Log.d(TAG, "NAVIGATION_INVESTIGATION: DynamicNavigationManager result: $navigationSuccess")

            if (!navigationSuccess) {
                Log.w(TAG, "NAVIGATION_INVESTIGATION: ❌ DynamicNavigationManager failed, attempting fallback navigation")
                val fallbackResult = performFallbackNavigation(targetFragmentId)
                Log.d(TAG, "NAVIGATION_INVESTIGATION: Fallback navigation result: $fallbackResult")
                navigationSuccess = fallbackResult
            } else {
                Log.d(TAG, "NAVIGATION_INVESTIGATION: ✅ DynamicNavigationManager navigation successful")
            }
        } else {
            Log.w(TAG, "NAVIGATION_INVESTIGATION: ❌ DynamicNavigationManager not initialized, using fallback")
            val targetFragmentId = if (isDeviceCharging) R.id.chargeFragment else R.id.dischargeFragment
            val fallbackResult = performFallbackNavigation(targetFragmentId)
            Log.d(TAG, "NAVIGATION_INVESTIGATION: Fallback navigation result: $fallbackResult")
            navigationSuccess = fallbackResult
        }

        Log.d(TAG, "NAVIGATION_INVESTIGATION: === LEGACY CHARGE/DISCHARGE NAVIGATION ${if (navigationSuccess) "SUCCESS" else "FAILED"} ===")
        return navigationSuccess
    }

    /**
     * Performs fallback navigation when DynamicNavigationManager fails.
     * Uses direct fragment manager navigation.
     * @return true if navigation was successful, false otherwise
     */
    private fun performFallbackNavigation(fragmentId: Int): Boolean {
        Log.d(TAG, "NAVIGATION_INVESTIGATION: === FALLBACK NAVIGATION START ===")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Target fragment ID: $fragmentId")

        return try {
            val fragment = when (fragmentId) {
                R.id.chargeFragment -> {
                    Log.d(TAG, "NAVIGATION_INVESTIGATION: Creating StatsChargeFragment")
                    com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment()
                }
                R.id.dischargeFragment -> {
                    Log.d(TAG, "NAVIGATION_INVESTIGATION: Creating DischargeFragment")
                    com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment()
                }
                else -> {
                    Log.e(TAG, "NAVIGATION_INVESTIGATION: ❌ Unknown fragment ID: $fragmentId")
                    return false
                }
            }

            Log.d(TAG, "NAVIGATION_INVESTIGATION: Fragment created successfully: ${fragment.javaClass.simpleName}")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: Using activity fragment manager for navigation")

            // Use activity's fragment manager for proper navigation
            requireActivity().supportFragmentManager
                .beginTransaction()
                .replace(R.id.nav_host_fragment, fragment)
                .addToBackStack(null)
                .commit()

            Log.d(TAG, "NAVIGATION_INVESTIGATION: ✅ Fallback navigation transaction committed successfully")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: === FALLBACK NAVIGATION SUCCESS ===")
            true

        } catch (e: Exception) {
            Log.e(TAG, "NAVIGATION_INVESTIGATION: ❌ Exception in fallback navigation", e)
            Log.e(TAG, "NAVIGATION_INVESTIGATION: Exception type: ${e.javaClass.simpleName}")
            Log.e(TAG, "NAVIGATION_INVESTIGATION: Exception message: ${e.message}")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: === FALLBACK NAVIGATION FAILED ===")
            false
        }
    }

    /**
     * Navigates to the specified fragment using fragment manager.
     * Fallback method for direct fragment navigation.
     */
    private fun navigateToFragment(fragment: androidx.fragment.app.Fragment) {
        Log.d(TAG, "OTHERS_NAVIGATION: Direct fragment navigation to ${fragment.javaClass.simpleName}")

        parentFragmentManager.beginTransaction()
            .replace(R.id.nav_host_fragment, fragment)
            .addToBackStack(null)
            .commit()
    }

    /**
     * Initializes the AppNavigator with required dependencies.
     * This enables centralized navigation management.
     */
    private fun initializeAppNavigator() {
        Log.d(TAG, "APPNAVIGATOR_INIT: Initializing AppNavigator from OthersFragment")

        try {
            val activity = requireActivity()
            val fragmentManager = activity.supportFragmentManager
            val bottomNavigationView = activity.findViewById<com.google.android.material.bottomnavigation.BottomNavigationView>(R.id.bottom_view)
            val fragmentContainerId = R.id.nav_host_fragment

            if (bottomNavigationView != null) {
                appNavigator.initialize(
                    fragmentManager = fragmentManager,
                    bottomNavigationView = bottomNavigationView,
                    fragmentContainerId = fragmentContainerId,
                    lifecycleOwner = this
                )
                Log.d(TAG, "APPNAVIGATOR_INIT: AppNavigator initialized successfully")
            } else {
                Log.e(TAG, "APPNAVIGATOR_INIT: BottomNavigationView not found, AppNavigator initialization failed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "APPNAVIGATOR_INIT: Error initializing AppNavigator", e)
        }
    }

    /**
     * Observes battery status changes from CoreBatteryStatsProvider.
     * Updates UI when charging state changes with debouncing to prevent flickering.
     * BATTERY_OBSERVATION_FIX: Prevents multiple concurrent observations and adds debouncing.
     */
    private fun observeBatteryStatus() {
        // BATTERY_OBSERVATION_FIX: Prevent multiple concurrent observations
        if (isBatteryObservationActive) {
            Log.d(TAG, "OTHERS_BATTERY: Battery observation already active, skipping duplicate setup")
            return
        }

        Log.d(TAG, "OTHERS_BATTERY: Starting battery status observation")
        isBatteryObservationActive = true

        lifecycleScope.launch {
            try {
                Log.d(TAG, "OTHERS_BATTERY: Collecting from coreBatteryStatusFlow")
                coreBatteryStatsProvider.coreBatteryStatusFlow
                    .filterNotNull()
                    .collect { status ->
                        val currentTime = System.currentTimeMillis()
                        Log.d(TAG, "OTHERS_BATTERY: Received battery status - charging: ${status.isCharging}, percentage: ${status.percentage}")

                        val wasCharging = isDeviceCharging
                        isDeviceCharging = status.isCharging

                        if (wasCharging != isDeviceCharging) {
                            Log.d(TAG, "OTHERS_BATTERY: Charging state changed from $wasCharging to $isDeviceCharging")

                            // BUTTON_FLICKERING_FIX: Debounce UI updates to prevent rapid changes
                            val timeSinceLastUpdate = currentTime - lastBatteryStateUpdateTime
                            if (timeSinceLastUpdate < 500) { // 500ms debounce
                                Log.d(TAG, "BUTTON_FLICKERING_FIX: Debouncing UI update (${timeSinceLastUpdate}ms since last)")
                                scheduleDebounceUIUpdate()
                            } else {
                                Log.d(TAG, "BUTTON_FLICKERING_FIX: Immediate UI update (${timeSinceLastUpdate}ms since last)")
                                lastBatteryStateUpdateTime = currentTime
                                updateAdapterItems()
                            }
                        } else {
                            Log.v(TAG, "OTHERS_BATTERY: Charging state unchanged: $isDeviceCharging")
                        }
                    }
            } catch (e: Exception) {
                Log.e(TAG, "OTHERS_BATTERY: Error observing battery status", e)
                isBatteryObservationActive = false // Reset flag on error

                // Fallback to default state and try to get current status
                try {
                    val currentStatus = coreBatteryStatsProvider.getCurrentStatus()
                    if (currentStatus != null) {
                        Log.d(TAG, "OTHERS_BATTERY: Using current status as fallback - charging: ${currentStatus.isCharging}")
                        isDeviceCharging = currentStatus.isCharging
                    } else {
                        Log.w(TAG, "OTHERS_BATTERY: No current status available, defaulting to not charging")
                        isDeviceCharging = false
                    }
                } catch (fallbackException: Exception) {
                    Log.e(TAG, "OTHERS_BATTERY: Error getting current status", fallbackException)
                    isDeviceCharging = false
                }
                updateAdapterItems()
            }
        }
    }

    /**
     * BUTTON_FLICKERING_FIX: Schedules a debounced UI update to prevent rapid button state changes.
     */
    private fun scheduleDebounceUIUpdate() {
        // Cancel any pending update
        pendingUIUpdateJob?.cancel()

        pendingUIUpdateJob = lifecycleScope.launch {
            delay(300) // 300ms debounce delay
            Log.d(TAG, "BUTTON_FLICKERING_FIX: Executing debounced UI update")
            lastBatteryStateUpdateTime = System.currentTimeMillis()
            updateAdapterItems()
        }
    }

    /**
     * Sets up the anti-theft toggle switch functionality.
     * Handles toggle state changes and password management.
     */
    private fun setupAntiTheftToggle() {
        Log.d(TAG, "OTHERS_ANTITHEFT: Setting up anti-theft toggle")

        // Initialize toggle state
        binding.switchEnableAntiThief.isChecked = appViewModel.isAntiThiefEnabled()
        Log.d(TAG, "OTHERS_ANTITHEFT: Initial toggle state - ${binding.switchEnableAntiThief.isChecked}")

        // Set toggle change listener
        binding.switchEnableAntiThief.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "OTHERS_ANTITHEFT: Toggle changed - $isChecked")
            handleAntiTheftToggle(isChecked)
        }

        // Set info icon click listener
        binding.antiThiefInfo.setOnClickListener {
            Log.d(TAG, "OTHERS_ANTITHEFT: Info icon clicked")
            showAntiTheftInfoDialog()
        }
    }

    /**
     * Handles anti-theft toggle state changes.
     * Shows password dialog if needed or updates preference directly.
     */
    private fun handleAntiTheftToggle(isEnabled: Boolean) {
        Log.d(TAG, "OTHERS_ANTITHEFT: Handling toggle change - enabled: $isEnabled")

        if (isEnabled && !appViewModel.isAntiThiefPasswordSet()) {
            Log.d(TAG, "OTHERS_ANTITHEFT: Enabling anti-theft but no password set - showing password dialog")
            showPasswordSetupDialog()
        } else {
            Log.d(TAG, "OTHERS_ANTITHEFT: Updating anti-theft preference - $isEnabled")
            appViewModel.setAntiThiefEnabled(isEnabled)
        }
    }

    /**
     * Shows the password setup dialog for anti-theft feature.
     */
    private fun showPasswordSetupDialog() {
        Log.d(TAG, "OTHERS_ANTITHEFT: Showing password setup dialog")

        try {
            val dialog = SetupPasswordDialog(
                context = requireContext(),
                onConfirm = { password ->
                    Log.d(TAG, "OTHERS_ANTITHEFT: Password set successfully")
                    appViewModel.setAntiThiefPassword(password)
                    appViewModel.setAntiThiefEnabled(true)
                    // Refresh toggle state to reflect the change
                    binding.switchEnableAntiThief.isChecked = true
                    Log.d(TAG, "OTHERS_ANTITHEFT: Toggle state updated after password setup")
                },
                onCancel = {
                    Log.d(TAG, "OTHERS_ANTITHEFT: Password setup cancelled")
                    // Reset toggle state if cancelled
                    binding.switchEnableAntiThief.isChecked = false
                }
            )
            dialog.show()
        } catch (e: Exception) {
            Log.e(TAG, "OTHERS_ANTITHEFT: Error showing password setup dialog", e)
            // Reset toggle state if dialog fails
            binding.switchEnableAntiThief.isChecked = false
        }
    }

    /**
     * Shows information dialog about the anti-theft feature.
     */
    private fun showAntiTheftInfoDialog() {
        Log.d(TAG, "OTHERS_ANTITHEFT: Showing anti-theft info dialog")

        val dialog = NotificationDialog(
            requireContext(),
            getString(R.string.anti_thief),
            getString(R.string.anti_thief_info)
        )
        dialog.show()

        Log.d(TAG, "OTHERS_ANTITHEFT: Anti-theft info dialog displayed")
    }

    /**
     * Observes SharedNavigationViewModel state changes for fragment self-management.
     * This fixes the button state flicker issue by ensuring proper state synchronization.
     */
    private fun observeNavigationState() {
        Log.d(TAG, "SharedNavigationViewModel: Setting up navigation state observation")

        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                sharedNavigationViewModel.activeFragmentId.collect { activeFragmentId ->
                    Log.d(TAG, "SharedNavigationViewModel: Navigation state changed - activeFragment: ${getFragmentName(activeFragmentId)}")

                    if (activeFragmentId == R.id.othersFragment) {
                        onFragmentVisible()
                    } else {
                        onFragmentHidden()
                    }
                }
            }
        }
    }

    /**
     * Called when this fragment becomes visible/active.
     * Handles UI refresh and state synchronization to prevent blank fragment issues.
     */
    private fun onFragmentVisible() {
        Log.d(TAG, "SharedNavigationViewModel: OthersFragment is ACTIVE")
        Log.d(TAG, "SharedNavigationViewModel: OthersFragment is now VISIBLE")
        Log.d(TAG, "FRAGMENT_LIFECYCLE: Fragment activated via SharedNavigationViewModel")

        // BUTTON_FLICKERING_FIX: Debounce UI updates to prevent rapid state changes
        pendingUIUpdateJob?.cancel()
        pendingUIUpdateJob = viewLifecycleOwner.lifecycleScope.launch {
            // Small delay to ensure battery state is synchronized
            kotlinx.coroutines.delay(50)

            // Refresh UI state to prevent blank fragment and button flicker
            if (::othersAdapter.isInitialized) {
                Log.d(TAG, "SharedNavigationViewModel: Refreshing adapter items for state synchronization")
                updateAdapterItems()
            }

            // Ensure battery observation is active
            if (!isBatteryObservationActive) {
                Log.d(TAG, "SharedNavigationViewModel: Restarting battery observation")
                observeBatteryStatus()
            }
        }
    }

    /**
     * Called when this fragment becomes hidden/inactive.
     * Handles cleanup and state preservation.
     */
    private fun onFragmentHidden() {
        Log.d(TAG, "SharedNavigationViewModel: OthersFragment is INACTIVE")
        Log.d(TAG, "SharedNavigationViewModel: OthersFragment is now HIDDEN")

        // Cancel any pending UI updates when fragment becomes inactive
        pendingUIUpdateJob?.cancel()
        Log.d(TAG, "SharedNavigationViewModel: Cancelled pending UI updates")
    }

    /**
     * Helper method to get human-readable fragment names for debugging.
     */
    private fun getFragmentName(fragmentId: Int): String {
        return when (fragmentId) {
            R.id.animationGridFragment -> "AnimationGridFragment"
            R.id.othersFragment -> "OthersFragment"
            R.id.chargeFragment -> "ChargeFragment"
            R.id.dischargeFragment -> "DischargeFragment"
            R.id.healthFragment -> "HealthFragment"
            R.id.settingsFragment -> "SettingsFragment"
            else -> "Unknown($fragmentId)"
        }
    }
}