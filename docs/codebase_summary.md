## Codebase Summary: Battery Charging Animation 3D
Bundle id: com.fc.p.tj.charginganimation.batterycharging.chargeeffect

### 1. Overall Purpose

The application is a comprehensive battery utility built on a **modern, unified architecture** designed to provide users with detailed, consistent, and reliable statistics about their device's battery. The core of the application is the `CoreBatteryStatsService`, which acts as a **single source of truth** for all battery-related data (charging state, percentage, current, temperature). This central provider feeds specialized feature modules for **Charging**, **Discharging**, and a new **Health** section. The app offers aesthetic enhancements like charging animations, security features like anti-theft protection, and configurable battery alarms, all powered by this unified data source.

### 2. Key Features

*   **Unified Battery Statistics (Core Architecture):**
    *   A central `CoreBatteryStatsService` and `CoreBatteryStatsProvider` (`features/stats/corebattery`) now manage all raw battery data collection, eliminating redundant services and ensuring data consistency across the app.
    *   All other features (Charge, Discharge, Health, Notifications) consume data from this single provider.

*   **Charge & Discharge Features (`features/stats` package):**
    *   **Charge Feature:**
        *   Monitors real-time charging metrics from the core provider.
        *   Tracks detailed charge sessions, including mAh charged, screen on/off time, and average rates.
        *   Provides accurate time-to-full and time-to-target estimates.
        *   UI is handled by `StatsChargeFragment` and `StatsChargeViewModel`.
    *   **Discharge Feature:**
        *   Provides robust and detailed tracking of discharge sessions.
        *   Calculates mAh-based consumption and offers time estimates based on learned discharge rates.
        *   Handles gap estimation (`GapEstimationCalculator`) for app restarts to ensure continuous tracking.
        *   UI is handled by `DischargeFragment` and `DischargeViewModel`.

*   **NEW: Battery Health Feature (`features/stats/health` package):**
    *   Calculates and displays a **battery health percentage** based on the number of charge cycles.
    *   Shows **Design Capacity** vs. health-adjusted **Effective Capacity**.
    *   Visualizes historical battery data with charts for **Battery Percentage** and **Temperature** over 4, 8, 12, and 24-hour periods.
    *   Displays a 7-day **Daily Wear** chart.
    *   Powered by its own clean architecture stack: `HealthRepository`, `HealthViewModel`, `CalculateBatteryHealthUseCase`, etc.

*   **Charging Animations:**
    *   Selection and application of video animations during charging (`AnimationActivity`).
    *   Animations sourced from Firebase Remote Config and managed by `AnimationRepository`.
    *   `ChargingOverlayService` displays the animation on the lock screen.

*   **Customization:**
    *   Theme selection (Light, Dark, Amoled, Auto) and accent color customization via `ThemeManager`.
    *   Multi-language support managed by `AppRepository`.

*   **NEW: Unified Alarms & Notifications (`features/stats/notifications` package):**
    *   A single `UnifiedBatteryNotificationService` now handles all alarms, consuming data from the core provider.
    *   Provides notifications for target charge reached, full charge, and charging state changes.
    *   Handles low battery alerts.
    *   Integrates anti-theft protection.
    *   Features adaptive update frequency and content caching for improved battery efficiency.

*   **Anti-Theft Protection:**
    *   Alerts if the charger is disconnected while the feature is active.
    *   Requires a user-set password (`SetupPasswordDialog`, `EnterPasswordActivity`).

*   **Onboarding & Permissions (`StartingActivity`):**
    *   Guides new users through privacy policy and necessary permissions.
    *   Includes a new, improved `BackgroundPermissionManager` to handle battery optimization requests with rate-limiting.

*   **Monetization (Inferred):**
    *   AppLovin Ad SDK integration (`Applovin...AdManager` classes). *Core ad-showing logic is currently commented out.*
    *   Firebase Remote Config for feature flags.
    *   Premium animations with a 24-hour trial system.

### 3. Project Structure

The project has been significantly refactored into a modular, stats-based architecture. Legacy feature modules have been replaced or standardized.

```
├── java/com/tqhit/battery/one/
│   ├── activity/
│   │   └── main/handlers/           # NEW: Handlers for MainActivity logic
│   ├── ads/core/
│   ├── component/progress/
│   ├── dialog/
│   ├── features/
│   │   ├── navigation/              # NEW: Dynamic navigation logic
│   │   └── stats/                   # NEW: Centralized stats modules
│   │       ├── apppower/            # NEW: App power consumption feature
│   │       ├── charge/              # REFACTORED: Charge feature
│   │       ├── corebattery/         # NEW: Core battery data source
│   │       ├── discharge/           # REFACTORED: Discharge feature
│   │       ├── health/              # NEW: Battery health feature
│   │       └── notifications/       # NEW: Unified notification service
│   ├── fragment/main/
│   ├── manager/                     # Legacy managers
│   ├── repository/                  # General-purpose repositories
│   ├── service/                     # General/Legacy services
│   ├── utils/
│   ├── viewmodel/
│   └── BatteryApplication.kt
├── res/
│   ├── layout/                      # Includes fragment_health, fragment_others
...
```

### 4. File Breakdown and Summaries

#### 4.1. Core Application & Setup

*   **`BatteryApplication.kt`**: Initializes the app. Starts the `CoreBatteryServiceHelper` to ensure the single battery service is running.
*   **`CoreBatteryStatsService.kt`**: **The new heart of the app.** A single, reliable foreground service that monitors `ACTION_BATTERY_CHANGED` and provides a `CoreBatteryStatus` object to the rest of the app via `CoreBatteryStatsProvider`.

#### 4.2. Activities

*   **`MainActivity.kt`**: Main UI host. **Refactored significantly** to delegate logic to handlers:
    *   `FragmentLifecycleManager`: Manages the initial fragment setup.
    *   `NavigationHandler`: Manages the `BottomNavigationView` and fragment transactions, powered by the new `DynamicNavigationManager`.
    *   `ServiceManager`: Orchestrates the startup of essential services like `UnifiedBatteryNotificationService` and `EnhancedDischargeTimerService`.
*   **Other Activities** (`AnimationActivity`, `ChargingOverlayActivity`, `EnterPasswordActivity`, `SplashActivity`, `StartingActivity`): Functionality remains largely the same.

#### 4.3. Fragments

*   **`DischargeFragment.kt` (`features/stats/discharge/presentation`)**: Primary UI for discharging information.
*   **`StatsChargeFragment.kt` (`features/stats/charge/presentation`)**: Primary UI for charging information.
*   **`HealthFragment.kt`**: **New fragment** to display battery health percentage, capacity details, and historical charts.
*   **`OthersFragment.kt`**: Serves as a navigation hub to access Charge/Discharge, Health, and Settings.
*   **`SettingsFragment.kt`**: UI for app settings. Now includes a button for battery optimization permissions.
*   **`AnimationGridFragment.kt`**: UI for charging animations.

#### 4.4. ViewModels

*   **`HealthViewModel.kt`**: **New ViewModel** for `HealthFragment`. Collects data from `HealthRepository` and exposes `HealthUiState`.
*   **`StatsChargeViewModel.kt`** & **`DischargeViewModel.kt`**: ViewModels for their respective fragments, now consuming data from the new stats repositories.

#### 4.5. Repositories & Data Sources

*   **`CoreBatteryStatsProvider.kt`**: **Key component.** A singleton that holds the `StateFlow<CoreBatteryStatus>` provided by the `CoreBatteryStatsService`, serving as the single source of truth.
*   **`HealthRepository.kt`**: **New repository.** Manages all data related to battery health, including calculations and historical chart data.
*   **`HistoryBatteryRepository.kt`**: **New repository.** Collects and persists real battery and temperature data from the `CoreBatteryStatsProvider` for use in the `HealthFragment` charts.
*   **`StatsChargeRepository.kt` & `DischargeSessionRepository.kt`**: Refactored repositories that now consume data from `CoreBatteryStatsProvider` instead of having their own data sources.

#### 4.6. Domain Logic / Use Cases

*   **`CalculateBatteryHealthUseCase.kt` & `GetHealthHistoryUseCase.kt`**: **New Use Cases** for the health feature, encapsulating business logic for health calculation and chart data processing.
*   Other use cases for charge and discharge features remain, but now operate on data from the unified core source.

#### 4.7. Services

*   **`CoreBatteryStatsService.kt`**: The single service for monitoring battery state.
*   **`UnifiedBatteryNotificationService.kt`**: The single service for handling all battery-related notifications and alarms.
*   **`EnhancedDischargeTimerService.kt`**: A robust background timer to ensure accurate screen on/off time tracking for the discharge feature.
*   **Legacy Services** (`BatteryMonitorService`, `ChargeMonitorService`, etc.): No longer started by the application but kept for reference. Their functionality has been consolidated into the new unified services.

#### 4.8. Navigation

*   **`DynamicNavigationManager.kt`**: **New, sophisticated navigation system.**
    *   Manages fragment switching using a more performant `show/hide` pattern.
    *   Dynamically swaps between the Charge and Discharge fragments based on the real-time charging state from `CoreBatteryStatsProvider`.
    *   Handles navigation state restoration and complex back stack behavior.

#### 4.9. Utilities (`utils/`)

*   **`BackgroundPermissionManager.kt`**: **New utility** to manage battery optimization permission requests with rate-limiting to improve user experience.
*   **`BatteryLogger.kt`**: **New centralized logging utility** that enables/disables logging based on the build type (debug/release) for better performance and cleaner production logs.