#!/bin/bash

# Navigation Testing Script for Android Battery Monitoring App
# This script automates the ADB testing protocol for navigation fixes

# App configuration
APP_PACKAGE="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
MAIN_ACTIVITY="$APP_PACKAGE/.activity.main.MainActivity"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if device is connected
check_device() {
    print_status "Checking ADB device connection..."
    if ! adb devices | grep -q "device$"; then
        print_error "No ADB device connected. Please connect a device or start an emulator."
        exit 1
    fi
    print_success "ADB device connected"
}

# Function to install and launch app
setup_app() {
    print_status "Setting up app for testing..."
    
    # Check if APK file exists
    if [ ! -f "app-debug.apk" ] && [ ! -f "app/build/outputs/apk/debug/app-debug.apk" ]; then
        print_warning "APK file not found. Please build the app first or provide APK path."
        read -p "Enter APK path (or press Enter to skip installation): " apk_path
        if [ -n "$apk_path" ] && [ -f "$apk_path" ]; then
            print_status "Installing APK: $apk_path"
            adb install -r "$apk_path"
        fi
    else
        # Try to find and install APK
        if [ -f "app-debug.apk" ]; then
            print_status "Installing app-debug.apk"
            adb install -r app-debug.apk
        elif [ -f "app/build/outputs/apk/debug/app-debug.apk" ]; then
            print_status "Installing app/build/outputs/apk/debug/app-debug.apk"
            adb install -r app/build/outputs/apk/debug/app-debug.apk
        fi
    fi
    
    # Launch app
    print_status "Launching app..."
    adb shell am start -n "$MAIN_ACTIVITY"
    sleep 2
    print_success "App launched"
}

# Function to start logcat monitoring
start_logcat() {
    local filter="$1"
    local output_file="$2"
    
    print_status "Starting logcat monitoring with filter: $filter"
    print_status "Output will be saved to: $output_file"
    print_status "Press Ctrl+C to stop monitoring"
    
    # Clear previous logs
    adb logcat -c
    
    # Start filtered logcat
    adb logcat | grep -E "$filter" | tee "$output_file"
}

# Function to simulate battery states
simulate_battery() {
    local state="$1"
    
    case $state in
        "charging")
            print_status "Simulating charging state (AC connected)"
            adb shell dumpsys battery set ac 1
            ;;
        "discharging")
            print_status "Simulating discharging state (AC disconnected)"
            adb shell dumpsys battery set ac 0
            ;;
        "reset")
            print_status "Resetting to actual battery state"
            adb shell dumpsys battery reset
            ;;
        *)
            print_error "Invalid battery state. Use: charging, discharging, or reset"
            return 1
            ;;
    esac
    
    # Show current battery status
    print_status "Current battery status:"
    adb shell dumpsys battery | grep -E "(AC powered|level)"
}

# Function to run automated test scenario
run_test_scenario() {
    local scenario="$1"
    local log_file="test_logs_$(date +%Y%m%d_%H%M%S).txt"
    
    print_status "Running test scenario: $scenario"
    print_status "Logs will be saved to: $log_file"
    
    case $scenario in
        "button_flickering")
            print_status "=== BUTTON FLICKERING TEST ==="
            print_status "1. Starting logcat monitoring for button state changes"
            echo "=== BUTTON FLICKERING TEST - $(date) ===" > "$log_file"
            
            # Start background logcat
            adb logcat -c
            timeout 60 adb logcat | grep -E "(BUTTON_STATE|UI_UPDATE|BATTERY_STATE_CHANGE)" >> "$log_file" &
            LOGCAT_PID=$!
            
            print_status "2. Navigate to Health Fragment and back to test button state"
            print_status "   - Go to Others Fragment"
            print_status "   - Tap Health card"
            print_status "   - Press back button"
            print_status "   - Observe button state in Others Fragment"
            
            read -p "Press Enter when you've completed the navigation test..."
            
            # Stop logcat
            kill $LOGCAT_PID 2>/dev/null
            print_success "Button flickering test completed. Check $log_file for results."
            ;;
            
        "back_navigation")
            print_status "=== BACK NAVIGATION TEST ==="
            echo "=== BACK NAVIGATION TEST - $(date) ===" > "$log_file"
            
            # Test charging state
            print_status "Testing back navigation from Charge Fragment"
            simulate_battery "charging"
            sleep 2
            
            adb logcat -c
            timeout 30 adb logcat | grep -E "(BACK_STACK_MGMT|NAVIGATION_FLOW|BACK_NAVIGATION)" >> "$log_file" &
            LOGCAT_PID=$!
            
            print_status "Navigate: Others → Charge → Back"
            read -p "Press Enter when you've tested Charge Fragment back navigation..."
            kill $LOGCAT_PID 2>/dev/null
            
            # Test discharging state
            print_status "Testing back navigation from Discharge Fragment"
            simulate_battery "discharging"
            sleep 2
            
            timeout 30 adb logcat | grep -E "(BACK_STACK_MGMT|NAVIGATION_FLOW|BACK_NAVIGATION)" >> "$log_file" &
            LOGCAT_PID=$!
            
            print_status "Navigate: Others → Discharge → Back"
            read -p "Press Enter when you've tested Discharge Fragment back navigation..."
            kill $LOGCAT_PID 2>/dev/null
            
            print_success "Back navigation test completed. Check $log_file for results."
            ;;
            
        "dynamic_switching")
            print_status "=== DYNAMIC SWITCHING TEST ==="
            echo "=== DYNAMIC SWITCHING TEST - $(date) ===" > "$log_file"

            adb logcat -c
            timeout 60 adb logcat | grep -E "(BATTERY_STATE_CHANGE|DYNAMIC_SWITCHING|NAVIGATION_FLOW)" >> "$log_file" &
            LOGCAT_PID=$!

            print_status "1. Setting charging state and navigating to Charge Fragment"
            simulate_battery "charging"
            sleep 2

            print_status "2. Navigate to Charge Fragment, then change battery state"
            print_status "   - Go to Others Fragment"
            print_status "   - Tap Charge card"
            read -p "Press Enter when you're in Charge Fragment..."

            print_status "3. Changing to discharging state - fragment should switch automatically"
            simulate_battery "discharging"

            read -p "Press Enter when you've observed the dynamic switching..."
            kill $LOGCAT_PID 2>/dev/null

            print_success "Dynamic switching test completed. Check $log_file for results."
            ;;

        "blank_others_fragment")
            print_status "=== BLANK OTHERS FRAGMENT TEST ==="
            print_status "Testing specific navigation sequence: Others → Health/Settings → Others → Charge → Back"
            echo "=== BLANK OTHERS FRAGMENT TEST - $(date) ===" > "$log_file"

            # Set charging state for this test
            simulate_battery "charging"
            sleep 2

            adb logcat -c
            timeout 120 adb logcat | grep -E "(OthersFragment|NAVIGATION_FLOW|BACK_STACK_MGMT|UI_UPDATE|FragmentLifecycleOptimizer|BACK_NAVIGATION_BLANK_FIX|ERROR)" >> "$log_file" &
            LOGCAT_PID=$!

            print_status "1. Navigate: Others Fragment → Health Fragment"
            print_status "   - Go to Others Fragment"
            print_status "   - Tap Health card"
            read -p "Press Enter when you're in Health Fragment..."

            print_status "2. Navigate: Health Fragment → Others Fragment (back button)"
            print_status "   - Press back button to return to Others Fragment"
            read -p "Press Enter when you're back in Others Fragment..."

            print_status "3. Navigate: Others Fragment → Charge Fragment"
            print_status "   - Tap Charge card (should show 'Charge' since device is charging)"
            read -p "Press Enter when you're in Charge Fragment..."

            print_status "4. Navigate: Charge Fragment → Others Fragment (back button)"
            print_status "   - Press back button to return to Others Fragment"
            print_status "   - OBSERVE: Does Others Fragment show properly or is it blank?"
            read -p "Press Enter after observing the Others Fragment state..."

            kill $LOGCAT_PID 2>/dev/null

            print_success "Blank Others Fragment test completed. Check $log_file for results."
            print_status "Look for BACK_NAVIGATION_BLANK_FIX logs to see if the fix is working."
            ;;
            
        *)
            print_error "Unknown test scenario. Available: button_flickering, back_navigation, dynamic_switching"
            return 1
            ;;
    esac
    
    # Reset battery state
    simulate_battery "reset"
}

# Function to show menu
show_menu() {
    echo
    echo "=== Navigation Testing Menu ==="
    echo "1. Setup app (install and launch)"
    echo "2. Start logcat monitoring"
    echo "3. Simulate battery states"
    echo "4. Run automated test scenarios"
    echo "5. Show current battery status"
    echo "6. Reset battery state"
    echo "7. Exit"
    echo
}

# Main script
main() {
    print_status "Android Battery App Navigation Testing Script"
    print_status "============================================="
    
    # Check device connection
    check_device
    
    while true; do
        show_menu
        read -p "Select option (1-7): " choice
        
        case $choice in
            1)
                setup_app
                ;;
            2)
                echo "Select logcat filter:"
                echo "1. Primary navigation monitoring (recommended)"
                echo "2. Button state and UI updates"
                echo "3. Back navigation and stack management"
                echo "4. All navigation logs (verbose)"
                read -p "Select filter (1-4): " filter_choice
                
                case $filter_choice in
                    1) filter="(OthersFragment|CoreBatteryStatsService|DynamicNavigationManager|NAVIGATION_FLOW|BATTERY_STATE_CHANGE|BUTTON_STATE|BACK_STACK_MGMT|UI_UPDATE|ERROR)" ;;
                    2) filter="(BUTTON_STATE|UI_UPDATE|BATTERY_STATE_CHANGE|BUTTON_FLICKERING_FIX)" ;;
                    3) filter="(BACK_STACK_MGMT|NAVIGATION_FLOW|BACK_NAVIGATION)" ;;
                    4) filter="(Navigation|Fragment|Battery|Charge|Discharge|Others)" ;;
                    *) filter="(OthersFragment|CoreBatteryStatsService|DynamicNavigationManager|ERROR)" ;;
                esac
                
                log_file="logcat_$(date +%Y%m%d_%H%M%S).txt"
                start_logcat "$filter" "$log_file"
                ;;
            3)
                echo "Select battery state:"
                echo "1. Charging (AC connected)"
                echo "2. Discharging (AC disconnected)"
                echo "3. Reset to actual state"
                read -p "Select state (1-3): " battery_choice
                
                case $battery_choice in
                    1) simulate_battery "charging" ;;
                    2) simulate_battery "discharging" ;;
                    3) simulate_battery "reset" ;;
                    *) print_error "Invalid choice" ;;
                esac
                ;;
            4)
                echo "Select test scenario:"
                echo "1. Button flickering test"
                echo "2. Back navigation test"
                echo "3. Dynamic switching test"
                echo "4. Blank Others Fragment test (specific sequence)"
                read -p "Select scenario (1-4): " scenario_choice

                case $scenario_choice in
                    1) run_test_scenario "button_flickering" ;;
                    2) run_test_scenario "back_navigation" ;;
                    3) run_test_scenario "dynamic_switching" ;;
                    4) run_test_scenario "blank_others_fragment" ;;
                    *) print_error "Invalid choice" ;;
                esac
                ;;
            5)
                print_status "Current battery status:"
                adb shell dumpsys battery
                ;;
            6)
                simulate_battery "reset"
                ;;
            7)
                print_status "Exiting..."
                exit 0
                ;;
            *)
                print_error "Invalid choice. Please select 1-7."
                ;;
        esac
    done
}

# Run main function
main "$@"
